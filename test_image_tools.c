#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "image_tools.h"

// Test data
static uint8_t test_rgb_data[] = {
    255, 0, 0,    // Red pixel
    0, 255, 0,    // Green pixel  
    0, 0, 255,    // Blue pixel
    128, 128, 128 // Gray pixel
};

static uint8_t test_bgr_data[] = {
    0, 0, 255,    // Red pixel (BGR format)
    0, 255, 0,    // Green pixel
    255, 0, 0,    // Blue pixel (BGR format)
    128, 128, 128 // Gray pixel
};

void test_swap_rgb_bgr() {
    printf("Testing SWAP_RGB_BGR...\n");
    
    uint8_t output[12];
    SWAP_RGB_BGR(test_rgb_data, output, 4);
    
    printf("Original RGB: ");
    for (int i = 0; i < 12; i++) {
        printf("%d ", test_rgb_data[i]);
    }
    printf("\n");
    
    printf("Swapped BGR: ");
    for (int i = 0; i < 12; i++) {
        printf("%d ", output[i]);
    }
    printf("\n\n");
}

void test_rgb_to_grayscale() {
    printf("Testing RGB888 to Y8 conversion...\n");
    
    uint8_t gray_output[4];
    rgb888_2_Y8(test_rgb_data, gray_output, 2, 2);
    
    printf("RGB pixels converted to grayscale: ");
    for (int i = 0; i < 4; i++) {
        printf("%d ", gray_output[i]);
    }
    printf("\n\n");
}

void test_bgr_to_grayscale() {
    printf("Testing BGR888 to Y8 conversion...\n");
    
    uint8_t gray_output[4];
    bgr888_2_Y8(test_bgr_data, gray_output, 2, 2);
    
    printf("BGR pixels converted to grayscale: ");
    for (int i = 0; i < 4; i++) {
        printf("%d ", gray_output[i]);
    }
    printf("\n\n");
}

void test_y8_to_y4() {
    printf("Testing Y8 to Y4 conversion...\n");
    
    uint8_t y8_data[] = {240, 160, 80, 16}; // 4 grayscale values
    uint8_t y4_output[2]; // Should pack into 2 bytes
    
    Y8ToY4(y8_data, y4_output, 2, 2);
    
    printf("Y8 input: ");
    for (int i = 0; i < 4; i++) {
        printf("%d ", y8_data[i]);
    }
    printf("\n");
    
    printf("Y4 packed output: ");
    for (int i = 0; i < 2; i++) {
        printf("0x%02X ", y4_output[i]);
    }
    printf("\n\n");
}

void test_rgb_to_monochrome() {
    printf("Testing RGB888 to Y1 conversion...\n");
    
    uint8_t mono_output[1]; // 4 pixels should pack into 1 byte (with 4 bits unused)
    rgb888_2_Y1(test_rgb_data, mono_output, 2, 2);
    
    printf("RGB pixels converted to 1-bit monochrome: 0x%02X\n", mono_output[0]);
    printf("Binary representation: ");
    for (int i = 7; i >= 0; i--) {
        printf("%d", (mono_output[0] >> i) & 1);
    }
    printf("\n\n");
}

void test_color_adjustment() {
    printf("Testing color adjustment...\n");
    
    uint8_t test_pixel[] = {128, 64, 192}; // Purple-ish pixel
    uint8_t original[] = {128, 64, 192};
    
    printf("Original pixel RGB: (%d, %d, %d)\n", original[0], original[1], original[2]);
    
    // Test with increased saturation and brightness
    adjustColor(test_pixel, 1.5f, 1.2f, 1.0f, 0.0f);
    
    printf("After adjustment RGB: (%d, %d, %d)\n", test_pixel[0], test_pixel[1], test_pixel[2]);
    printf("\n");
}

void test_nearest_color() {
    printf("Testing nearest color matching...\n");

    int red_index = find_nearest_color_AIO(255, 0, 0);
    int green_index = find_nearest_color_AIO(0, 255, 0);
    int blue_index = find_nearest_color_AIO(0, 0, 255);
    int gray_index = find_nearest_color_AIO(128, 128, 128);

    printf("Nearest color index for red (255,0,0): %d\n", red_index);
    printf("Nearest color index for green (0,255,0): %d\n", green_index);
    printf("Nearest color index for blue (0,0,255): %d\n", blue_index);
    printf("Nearest color index for gray (128,128,128): %d\n", gray_index);
    printf("\n");
}

void test_spectra6_index_mapping() {
    printf("Testing Spectra6 index mapping to Y4...\n");

    // Create test RGB data (2x2 image = 4 pixels)
    uint8_t test_rgb[] = {
        255, 0, 0,     // Red
        0, 255, 0,     // Green
        0, 0, 255,     // Blue
        255, 255, 0    // Yellow
    };

    uint8_t y4_output[2]; // 4 pixels packed into 2 bytes

    // Test the simple version (doesn't require external palette)
    IndexMapping_Spectra6_AIO_Y4_Simple(test_rgb, y4_output, 2, 2);

    printf("Input RGB pixels:\n");
    printf("  Pixel 0: RGB(%d, %d, %d)\n", test_rgb[0], test_rgb[1], test_rgb[2]);
    printf("  Pixel 1: RGB(%d, %d, %d)\n", test_rgb[3], test_rgb[4], test_rgb[5]);
    printf("  Pixel 2: RGB(%d, %d, %d)\n", test_rgb[6], test_rgb[7], test_rgb[8]);
    printf("  Pixel 3: RGB(%d, %d, %d)\n", test_rgb[9], test_rgb[10], test_rgb[11]);

    printf("Y4 packed output: ");
    for (int i = 0; i < 2; i++) {
        printf("0x%02X ", y4_output[i]);
    }
    printf("\n");

    // Decode and show the packed values
    printf("Decoded Y4 values:\n");
    printf("  Pixel 0 (upper 4 bits of byte 0): %d\n", (y4_output[0] >> 4) & 0x0F);
    printf("  Pixel 1 (lower 4 bits of byte 0): %d\n", y4_output[0] & 0x0F);
    printf("  Pixel 2 (upper 4 bits of byte 1): %d\n", (y4_output[1] >> 4) & 0x0F);
    printf("  Pixel 3 (lower 4 bits of byte 1): %d\n", y4_output[1] & 0x0F);
    printf("\n");
}

int main() {
    printf("=== Image Tools Test Suite ===\n\n");

    test_swap_rgb_bgr();
    test_rgb_to_grayscale();
    test_bgr_to_grayscale();
    test_y8_to_y4();
    test_rgb_to_monochrome();
    test_color_adjustment();
    test_nearest_color();
    test_spectra6_index_mapping();

    printf("All tests completed!\n");
    return 0;
}
