#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "image_tools.h"

// Demo function to create a test image with various colors
void create_test_image(uint8_t* image_data, int width, int height) {
    int pixel_idx = 0;
    
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            // Create a pattern with different colors
            if (x < width/3) {
                if (y < height/2) {
                    // Top-left: Red
                    image_data[pixel_idx] = 255;
                    image_data[pixel_idx + 1] = 0;
                    image_data[pixel_idx + 2] = 0;
                } else {
                    // Bottom-left: Green
                    image_data[pixel_idx] = 0;
                    image_data[pixel_idx + 1] = 255;
                    image_data[pixel_idx + 2] = 0;
                }
            } else if (x < 2*width/3) {
                if (y < height/2) {
                    // Top-middle: Blue
                    image_data[pixel_idx] = 0;
                    image_data[pixel_idx + 1] = 0;
                    image_data[pixel_idx + 2] = 255;
                } else {
                    // Bottom-middle: Yellow
                    image_data[pixel_idx] = 255;
                    image_data[pixel_idx + 1] = 255;
                    image_data[pixel_idx + 2] = 0;
                }
            } else {
                if (y < height/2) {
                    // Top-right: White
                    image_data[pixel_idx] = 255;
                    image_data[pixel_idx + 1] = 255;
                    image_data[pixel_idx + 2] = 255;
                } else {
                    // Bottom-right: Black
                    image_data[pixel_idx] = 0;
                    image_data[pixel_idx + 1] = 0;
                    image_data[pixel_idx + 2] = 0;
                }
            }
            pixel_idx += 3;
        }
    }
}

void print_rgb_image(uint8_t* image_data, int width, int height) {
    printf("RGB Image (%dx%d):\n", width, height);
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            int idx = (y * width + x) * 3;
            printf("(%3d,%3d,%3d) ", 
                   image_data[idx], 
                   image_data[idx + 1], 
                   image_data[idx + 2]);
        }
        printf("\n");
    }
    printf("\n");
}

void print_y4_image(uint8_t* y4_data, int width, int height) {
    printf("Y4 Packed Data:\n");
    int total_bytes = (width * height + 1) / 2; // Round up for odd pixel count
    
    printf("Raw bytes: ");
    for (int i = 0; i < total_bytes; i++) {
        printf("0x%02X ", y4_data[i]);
    }
    printf("\n");
    
    printf("Decoded Y4 values (%dx%d):\n", width, height);
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            int pixel_idx = y * width + x;
            int byte_idx = pixel_idx / 2;
            uint8_t value;
            
            if ((pixel_idx & 1) == 0) {
                // Even pixel - upper 4 bits
                value = (y4_data[byte_idx] >> 4) & 0x0F;
            } else {
                // Odd pixel - lower 4 bits
                value = y4_data[byte_idx] & 0x0F;
            }
            
            printf("%2d ", value);
        }
        printf("\n");
    }
    printf("\n");
}

void demonstrate_spectra6_mapping() {
    printf("=== Spectra6 Index Mapping Demonstration ===\n\n");
    
    // Create a 6x2 test image (12 pixels total)
    const int width = 6;
    const int height = 2;
    const int total_pixels = width * height;
    
    uint8_t* rgb_image = malloc(total_pixels * 3);
    uint8_t* y4_output = malloc((total_pixels + 1) / 2);
    
    if (!rgb_image || !y4_output) {
        printf("Memory allocation failed!\n");
        return;
    }
    
    // Create test image with color pattern
    create_test_image(rgb_image, width, height);
    
    // Display original RGB image
    print_rgb_image(rgb_image, width, height);
    
    // Apply Spectra6 index mapping
    printf("Applying IndexMapping_Spectra6_AIO_Y4_Simple...\n\n");
    IndexMapping_Spectra6_AIO_Y4_Simple(rgb_image, y4_output, width, height);
    
    // Display Y4 result
    print_y4_image(y4_output, width, height);
    
    // Show the mapping table
    printf("Spectra6 Color Mapping:\n");
    printf("Color          RGB           Index\n");
    printf("--------------------------------\n");
    printf("Black      -> (  0,  0,  0) -> 0\n");
    printf("White      -> (255,255,255) -> 15\n");
    printf("Red        -> (255,  0,  0) -> 8\n");
    printf("Green      -> (  0,255,  0) -> 4\n");
    printf("Blue       -> (  0,  0,255) -> 2\n");
    printf("Yellow     -> (255,255,  0) -> 12\n");
    printf("\n");
    
    // Analyze compression ratio
    int original_size = total_pixels * 3; // 3 bytes per pixel (RGB)
    int compressed_size = (total_pixels + 1) / 2; // 0.5 bytes per pixel (Y4)
    float compression_ratio = (float)original_size / compressed_size;
    
    printf("Compression Analysis:\n");
    printf("Original size (RGB888): %d bytes\n", original_size);
    printf("Compressed size (Y4):   %d bytes\n", compressed_size);
    printf("Compression ratio:      %.1f:1\n", compression_ratio);
    printf("Space savings:          %.1f%%\n", (1.0f - (float)compressed_size/original_size) * 100);
    
    free(rgb_image);
    free(y4_output);
}

void test_edge_cases() {
    printf("\n=== Edge Cases Testing ===\n\n");
    
    // Test 1: Single pixel
    printf("Test 1: Single pixel (1x1 image)\n");
    uint8_t single_pixel[3] = {255, 0, 0}; // Red
    uint8_t single_y4[1];
    
    IndexMapping_Spectra6_AIO_Y4_Simple(single_pixel, single_y4, 1, 1);
    printf("Input: RGB(255, 0, 0)\n");
    printf("Output: 0x%02X (value = %d)\n", single_y4[0], (single_y4[0] >> 4) & 0x0F);
    
    // Test 2: Odd number of pixels
    printf("\nTest 2: Odd pixel count (3x1 image)\n");
    uint8_t three_pixels[9] = {
        255, 0, 0,    // Red
        0, 255, 0,    // Green
        0, 0, 255     // Blue
    };
    uint8_t three_y4[2]; // 3 pixels need 2 bytes (1.5 bytes rounded up)
    
    IndexMapping_Spectra6_AIO_Y4_Simple(three_pixels, three_y4, 3, 1);
    printf("Input: Red, Green, Blue\n");
    printf("Output: 0x%02X 0x%02X\n", three_y4[0], three_y4[1]);
    printf("Pixel 0: %d, Pixel 1: %d, Pixel 2: %d\n",
           (three_y4[0] >> 4) & 0x0F,
           three_y4[0] & 0x0F,
           (three_y4[1] >> 4) & 0x0F);
    
    // Test 3: Unknown color (should map to nearest)
    printf("\nTest 3: Unknown color mapping\n");
    uint8_t unknown_color[3] = {128, 64, 192}; // Purple-ish
    uint8_t unknown_y4[1];
    
    IndexMapping_Spectra6_AIO_Y4_Simple(unknown_color, unknown_y4, 1, 1);
    printf("Input: RGB(128, 64, 192) - unknown color\n");
    printf("Output: 0x%02X (mapped to nearest color, value = %d)\n", 
           unknown_y4[0], (unknown_y4[0] >> 4) & 0x0F);
}

int main() {
    demonstrate_spectra6_mapping();
    test_edge_cases();
    
    printf("\n=== Demo completed! ===\n");
    return 0;
}
