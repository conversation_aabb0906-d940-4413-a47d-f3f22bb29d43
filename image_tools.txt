
image_tools.o：     文件格式 elf64-littleaarch64


Disassembly of section .text:

0000000000000000 <SWAP_RGB_BGR>:
       0:	d100c3ff 	sub	sp, sp, #0x30
       4:	f9000fe0 	str	x0, [sp, #24]
       8:	f9000be1 	str	x1, [sp, #16]
       c:	b9000fe2 	str	w2, [sp, #12]
      10:	b9002bff 	str	wzr, [sp, #40]
      14:	b9002fff 	str	wzr, [sp, #44]
      18:	14000023 	b	a4 <SWAP_RGB_BGR+0xa4>
      1c:	b9802be0 	ldrsw	x0, [sp, #40]
      20:	91000800 	add	x0, x0, #0x2
      24:	f9400fe1 	ldr	x1, [sp, #24]
      28:	8b000021 	add	x1, x1, x0
      2c:	b9802be0 	ldrsw	x0, [sp, #40]
      30:	f9400be2 	ldr	x2, [sp, #16]
      34:	8b000040 	add	x0, x2, x0
      38:	39400021 	ldrb	w1, [x1]
      3c:	39000001 	strb	w1, [x0]
      40:	b9802be0 	ldrsw	x0, [sp, #40]
      44:	91000400 	add	x0, x0, #0x1
      48:	f9400fe1 	ldr	x1, [sp, #24]
      4c:	8b000021 	add	x1, x1, x0
      50:	b9802be0 	ldrsw	x0, [sp, #40]
      54:	91000400 	add	x0, x0, #0x1
      58:	f9400be2 	ldr	x2, [sp, #16]
      5c:	8b000040 	add	x0, x2, x0
      60:	39400021 	ldrb	w1, [x1]
      64:	39000001 	strb	w1, [x0]
      68:	b9802be0 	ldrsw	x0, [sp, #40]
      6c:	f9400fe1 	ldr	x1, [sp, #24]
      70:	8b000021 	add	x1, x1, x0
      74:	b9802be0 	ldrsw	x0, [sp, #40]
      78:	91000800 	add	x0, x0, #0x2
      7c:	f9400be2 	ldr	x2, [sp, #16]
      80:	8b000040 	add	x0, x2, x0
      84:	39400021 	ldrb	w1, [x1]
      88:	39000001 	strb	w1, [x0]
      8c:	b9402be0 	ldr	w0, [sp, #40]
      90:	11000c00 	add	w0, w0, #0x3
      94:	b9002be0 	str	w0, [sp, #40]
      98:	b9402fe0 	ldr	w0, [sp, #44]
      9c:	11000400 	add	w0, w0, #0x1
      a0:	b9002fe0 	str	w0, [sp, #44]
      a4:	b9402fe1 	ldr	w1, [sp, #44]
      a8:	b9400fe0 	ldr	w0, [sp, #12]
      ac:	6b00003f 	cmp	w1, w0
      b0:	54fffb6b 	b.lt	1c <SWAP_RGB_BGR+0x1c>  // b.tstop
      b4:	d503201f 	nop
      b8:	9100c3ff 	add	sp, sp, #0x30
      bc:	d65f03c0 	ret

00000000000000c0 <bgr888_2_Y8>:
      c0:	d100c3ff 	sub	sp, sp, #0x30
      c4:	f9000fe0 	str	x0, [sp, #24]
      c8:	f9000be1 	str	x1, [sp, #16]
      cc:	b9000fe2 	str	w2, [sp, #12]
      d0:	b9000be3 	str	w3, [sp, #8]
      d4:	b9002fff 	str	wzr, [sp, #44]
      d8:	b9002bff 	str	wzr, [sp, #40]
      dc:	1400002c 	b	18c <bgr888_2_Y8+0xcc>
      e0:	b9802fe0 	ldrsw	x0, [sp, #44]
      e4:	f9400fe1 	ldr	x1, [sp, #24]
      e8:	8b000020 	add	x0, x1, x0
      ec:	39400000 	ldrb	w0, [x0]
      f0:	2a0003e1 	mov	w1, w0
      f4:	52800e40 	mov	w0, #0x72                  	// #114
      f8:	1b007c21 	mul	w1, w1, w0
      fc:	b9802fe0 	ldrsw	x0, [sp, #44]
     100:	91000400 	add	x0, x0, #0x1
     104:	f9400fe2 	ldr	x2, [sp, #24]
     108:	8b000040 	add	x0, x2, x0
     10c:	39400000 	ldrb	w0, [x0]
     110:	2a0003e2 	mov	w2, w0
     114:	52804960 	mov	w0, #0x24b                 	// #587
     118:	1b007c40 	mul	w0, w2, w0
     11c:	0b000021 	add	w1, w1, w0
     120:	b9802fe0 	ldrsw	x0, [sp, #44]
     124:	91000800 	add	x0, x0, #0x2
     128:	f9400fe2 	ldr	x2, [sp, #24]
     12c:	8b000040 	add	x0, x2, x0
     130:	39400000 	ldrb	w0, [x0]
     134:	2a0003e2 	mov	w2, w0
     138:	52802560 	mov	w0, #0x12b                 	// #299
     13c:	1b007c40 	mul	w0, w2, w0
     140:	0b000020 	add	w0, w1, w0
     144:	5289ba61 	mov	w1, #0x4dd3                	// #19923
     148:	72a20c41 	movk	w1, #0x1062, lsl #16
     14c:	9b217c01 	smull	x1, w0, w1
     150:	d360fc21 	lsr	x1, x1, #32
     154:	13067c21 	asr	w1, w1, #6
     158:	131f7c00 	asr	w0, w0, #31
     15c:	4b000022 	sub	w2, w1, w0
     160:	b9802be0 	ldrsw	x0, [sp, #40]
     164:	f9400be1 	ldr	x1, [sp, #16]
     168:	8b000020 	add	x0, x1, x0
     16c:	12001c41 	and	w1, w2, #0xff
     170:	39000001 	strb	w1, [x0]
     174:	b9402fe0 	ldr	w0, [sp, #44]
     178:	11000c00 	add	w0, w0, #0x3
     17c:	b9002fe0 	str	w0, [sp, #44]
     180:	b9402be0 	ldr	w0, [sp, #40]
     184:	11000400 	add	w0, w0, #0x1
     188:	b9002be0 	str	w0, [sp, #40]
     18c:	b9400be1 	ldr	w1, [sp, #8]
     190:	b9400be0 	ldr	w0, [sp, #8]
     194:	1b007c20 	mul	w0, w1, w0
     198:	b9402be1 	ldr	w1, [sp, #40]
     19c:	6b00003f 	cmp	w1, w0
     1a0:	54fffa0b 	b.lt	e0 <bgr888_2_Y8+0x20>  // b.tstop
     1a4:	d503201f 	nop
     1a8:	9100c3ff 	add	sp, sp, #0x30
     1ac:	d65f03c0 	ret

00000000000001b0 <rgb888_2_Y8>:
     1b0:	d100c3ff 	sub	sp, sp, #0x30
     1b4:	f9000fe0 	str	x0, [sp, #24]
     1b8:	f9000be1 	str	x1, [sp, #16]
     1bc:	b9000fe2 	str	w2, [sp, #12]
     1c0:	b9000be3 	str	w3, [sp, #8]
     1c4:	b9002fff 	str	wzr, [sp, #44]
     1c8:	b9002bff 	str	wzr, [sp, #40]
     1cc:	1400002c 	b	27c <rgb888_2_Y8+0xcc>
     1d0:	b9802fe0 	ldrsw	x0, [sp, #44]
     1d4:	f9400fe1 	ldr	x1, [sp, #24]
     1d8:	8b000020 	add	x0, x1, x0
     1dc:	39400000 	ldrb	w0, [x0]
     1e0:	2a0003e1 	mov	w1, w0
     1e4:	52802560 	mov	w0, #0x12b                 	// #299
     1e8:	1b007c21 	mul	w1, w1, w0
     1ec:	b9802fe0 	ldrsw	x0, [sp, #44]
     1f0:	91000400 	add	x0, x0, #0x1
     1f4:	f9400fe2 	ldr	x2, [sp, #24]
     1f8:	8b000040 	add	x0, x2, x0
     1fc:	39400000 	ldrb	w0, [x0]
     200:	2a0003e2 	mov	w2, w0
     204:	52804960 	mov	w0, #0x24b                 	// #587
     208:	1b007c40 	mul	w0, w2, w0
     20c:	0b000021 	add	w1, w1, w0
     210:	b9802fe0 	ldrsw	x0, [sp, #44]
     214:	91000800 	add	x0, x0, #0x2
     218:	f9400fe2 	ldr	x2, [sp, #24]
     21c:	8b000040 	add	x0, x2, x0
     220:	39400000 	ldrb	w0, [x0]
     224:	2a0003e2 	mov	w2, w0
     228:	52800e40 	mov	w0, #0x72                  	// #114
     22c:	1b007c40 	mul	w0, w2, w0
     230:	0b000020 	add	w0, w1, w0
     234:	5289ba61 	mov	w1, #0x4dd3                	// #19923
     238:	72a20c41 	movk	w1, #0x1062, lsl #16
     23c:	9b217c01 	smull	x1, w0, w1
     240:	d360fc21 	lsr	x1, x1, #32
     244:	13067c21 	asr	w1, w1, #6
     248:	131f7c00 	asr	w0, w0, #31
     24c:	4b000022 	sub	w2, w1, w0
     250:	b9802be0 	ldrsw	x0, [sp, #40]
     254:	f9400be1 	ldr	x1, [sp, #16]
     258:	8b000020 	add	x0, x1, x0
     25c:	12001c41 	and	w1, w2, #0xff
     260:	39000001 	strb	w1, [x0]
     264:	b9402fe0 	ldr	w0, [sp, #44]
     268:	11000c00 	add	w0, w0, #0x3
     26c:	b9002fe0 	str	w0, [sp, #44]
     270:	b9402be0 	ldr	w0, [sp, #40]
     274:	11000400 	add	w0, w0, #0x1
     278:	b9002be0 	str	w0, [sp, #40]
     27c:	b9400be1 	ldr	w1, [sp, #8]
     280:	b9400be0 	ldr	w0, [sp, #8]
     284:	1b007c20 	mul	w0, w1, w0
     288:	b9402be1 	ldr	w1, [sp, #40]
     28c:	6b00003f 	cmp	w1, w0
     290:	54fffa0b 	b.lt	1d0 <rgb888_2_Y8+0x20>  // b.tstop
     294:	d503201f 	nop
     298:	9100c3ff 	add	sp, sp, #0x30
     29c:	d65f03c0 	ret

00000000000002a0 <Y8ToY4>:
     2a0:	d100c3ff 	sub	sp, sp, #0x30
     2a4:	f9000fe0 	str	x0, [sp, #24]
     2a8:	f9000be1 	str	x1, [sp, #16]
     2ac:	b9000fe2 	str	w2, [sp, #12]
     2b0:	b9000be3 	str	w3, [sp, #8]
     2b4:	b9002fff 	str	wzr, [sp, #44]
     2b8:	b9002fff 	str	wzr, [sp, #44]
     2bc:	1400002c 	b	36c <Y8ToY4+0xcc>
     2c0:	b9402fe0 	ldr	w0, [sp, #44]
     2c4:	12000000 	and	w0, w0, #0x1
     2c8:	7100001f 	cmp	w0, #0x0
     2cc:	54000201 	b.ne	30c <Y8ToY4+0x6c>  // b.any
     2d0:	b9802fe0 	ldrsw	x0, [sp, #44]
     2d4:	f9400fe1 	ldr	x1, [sp, #24]
     2d8:	8b000020 	add	x0, x1, x0
     2dc:	39400001 	ldrb	w1, [x0]
     2e0:	b9402fe0 	ldr	w0, [sp, #44]
     2e4:	531f7c02 	lsr	w2, w0, #31
     2e8:	0b000040 	add	w0, w2, w0
     2ec:	13017c00 	asr	w0, w0, #1
     2f0:	93407c00 	sxtw	x0, w0
     2f4:	f9400be2 	ldr	x2, [sp, #16]
     2f8:	8b000040 	add	x0, x2, x0
     2fc:	53047c21 	lsr	w1, w1, #4
     300:	12001c21 	and	w1, w1, #0xff
     304:	39000001 	strb	w1, [x0]
     308:	14000016 	b	360 <Y8ToY4+0xc0>
     30c:	b9402fe0 	ldr	w0, [sp, #44]
     310:	531f7c01 	lsr	w1, w0, #31
     314:	0b000020 	add	w0, w1, w0
     318:	13017c00 	asr	w0, w0, #1
     31c:	2a0003e3 	mov	w3, w0
     320:	93407c60 	sxtw	x0, w3
     324:	f9400be1 	ldr	x1, [sp, #16]
     328:	8b000020 	add	x0, x1, x0
     32c:	39400002 	ldrb	w2, [x0]
     330:	b9802fe0 	ldrsw	x0, [sp, #44]
     334:	f9400fe1 	ldr	x1, [sp, #24]
     338:	8b000020 	add	x0, x1, x0
     33c:	39400000 	ldrb	w0, [x0]
     340:	121c6c00 	and	w0, w0, #0xfffffff0
     344:	12001c01 	and	w1, w0, #0xff
     348:	93407c60 	sxtw	x0, w3
     34c:	f9400be3 	ldr	x3, [sp, #16]
     350:	8b000060 	add	x0, x3, x0
     354:	0b010041 	add	w1, w2, w1
     358:	12001c21 	and	w1, w1, #0xff
     35c:	39000001 	strb	w1, [x0]
     360:	b9402fe0 	ldr	w0, [sp, #44]
     364:	11000400 	add	w0, w0, #0x1
     368:	b9002fe0 	str	w0, [sp, #44]
     36c:	b9400fe1 	ldr	w1, [sp, #12]
     370:	b9400be0 	ldr	w0, [sp, #8]
     374:	1b007c20 	mul	w0, w1, w0
     378:	b9402fe1 	ldr	w1, [sp, #44]
     37c:	6b00003f 	cmp	w1, w0
     380:	54fffa0b 	b.lt	2c0 <Y8ToY4+0x20>  // b.tstop
     384:	d503201f 	nop
     388:	9100c3ff 	add	sp, sp, #0x30
     38c:	d65f03c0 	ret

0000000000000390 <bgr888_2_Y1>:
     390:	d10103ff 	sub	sp, sp, #0x40
     394:	f9000fe0 	str	x0, [sp, #24]
     398:	f9000be1 	str	x1, [sp, #16]
     39c:	b9000fe2 	str	w2, [sp, #12]
     3a0:	b9000be3 	str	w3, [sp, #8]
     3a4:	b9003fff 	str	wzr, [sp, #60]
     3a8:	b9003bff 	str	wzr, [sp, #56]
     3ac:	b90037ff 	str	wzr, [sp, #52]
     3b0:	b90033ff 	str	wzr, [sp, #48]
     3b4:	3900bfff 	strb	wzr, [sp, #47]
     3b8:	b9003fff 	str	wzr, [sp, #60]
     3bc:	14000041 	b	4c0 <bgr888_2_Y1+0x130>
     3c0:	b9803be0 	ldrsw	x0, [sp, #56]
     3c4:	f9400fe1 	ldr	x1, [sp, #24]
     3c8:	8b000020 	add	x0, x1, x0
     3cc:	39400000 	ldrb	w0, [x0]
     3d0:	1e620001 	scvtf	d1, w0
     3d4:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
     3d8:	91000000 	add	x0, x0, #0x0
     3dc:	fd400000 	ldr	d0, [x0]
     3e0:	1e600821 	fmul	d1, d1, d0
     3e4:	b9803be0 	ldrsw	x0, [sp, #56]
     3e8:	91000400 	add	x0, x0, #0x1
     3ec:	f9400fe1 	ldr	x1, [sp, #24]
     3f0:	8b000020 	add	x0, x1, x0
     3f4:	39400000 	ldrb	w0, [x0]
     3f8:	1e620002 	scvtf	d2, w0
     3fc:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
     400:	91000000 	add	x0, x0, #0x0
     404:	fd400000 	ldr	d0, [x0]
     408:	1e600840 	fmul	d0, d2, d0
     40c:	1e602821 	fadd	d1, d1, d0
     410:	b9803be0 	ldrsw	x0, [sp, #56]
     414:	91000800 	add	x0, x0, #0x2
     418:	f9400fe1 	ldr	x1, [sp, #24]
     41c:	8b000020 	add	x0, x1, x0
     420:	39400000 	ldrb	w0, [x0]
     424:	1e620002 	scvtf	d2, w0
     428:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
     42c:	91000000 	add	x0, x0, #0x0
     430:	fd400000 	ldr	d0, [x0]
     434:	1e600840 	fmul	d0, d2, d0
     438:	1e602820 	fadd	d0, d1, d0
     43c:	1e790000 	fcvtzu	w0, d0
     440:	3900bbe0 	strb	w0, [sp, #46]
     444:	b9403be0 	ldr	w0, [sp, #56]
     448:	11000c00 	add	w0, w0, #0x3
     44c:	b9003be0 	str	w0, [sp, #56]
     450:	3940bfe0 	ldrb	w0, [sp, #47]
     454:	53017c00 	lsr	w0, w0, #1
     458:	3900bfe0 	strb	w0, [sp, #47]
     45c:	3940bbe0 	ldrb	w0, [sp, #46]
     460:	7102001f 	cmp	w0, #0x80
     464:	54000089 	b.ls	474 <bgr888_2_Y1+0xe4>  // b.plast
     468:	3940bfe0 	ldrb	w0, [sp, #47]
     46c:	32196000 	orr	w0, w0, #0xffffff80
     470:	3900bfe0 	strb	w0, [sp, #47]
     474:	b94033e0 	ldr	w0, [sp, #48]
     478:	11000400 	add	w0, w0, #0x1
     47c:	b90033e0 	str	w0, [sp, #48]
     480:	b94033e0 	ldr	w0, [sp, #48]
     484:	7100201f 	cmp	w0, #0x8
     488:	54000161 	b.ne	4b4 <bgr888_2_Y1+0x124>  // b.any
     48c:	b94037e0 	ldr	w0, [sp, #52]
     490:	11000401 	add	w1, w0, #0x1
     494:	b90037e1 	str	w1, [sp, #52]
     498:	93407c00 	sxtw	x0, w0
     49c:	f9400be1 	ldr	x1, [sp, #16]
     4a0:	8b000020 	add	x0, x1, x0
     4a4:	3940bfe1 	ldrb	w1, [sp, #47]
     4a8:	39000001 	strb	w1, [x0]
     4ac:	3900bfff 	strb	wzr, [sp, #47]
     4b0:	b90033ff 	str	wzr, [sp, #48]
     4b4:	b9403fe0 	ldr	w0, [sp, #60]
     4b8:	11000400 	add	w0, w0, #0x1
     4bc:	b9003fe0 	str	w0, [sp, #60]
     4c0:	b9400fe1 	ldr	w1, [sp, #12]
     4c4:	b9400be0 	ldr	w0, [sp, #8]
     4c8:	1b007c20 	mul	w0, w1, w0
     4cc:	b9403fe1 	ldr	w1, [sp, #60]
     4d0:	6b00003f 	cmp	w1, w0
     4d4:	54fff76b 	b.lt	3c0 <bgr888_2_Y1+0x30>  // b.tstop
     4d8:	d503201f 	nop
     4dc:	910103ff 	add	sp, sp, #0x40
     4e0:	d65f03c0 	ret

00000000000004e4 <rgb888_2_Y1>:
     4e4:	d10103ff 	sub	sp, sp, #0x40
     4e8:	f9000fe0 	str	x0, [sp, #24]
     4ec:	f9000be1 	str	x1, [sp, #16]
     4f0:	b9000fe2 	str	w2, [sp, #12]
     4f4:	b9000be3 	str	w3, [sp, #8]
     4f8:	b9003fff 	str	wzr, [sp, #60]
     4fc:	b9003bff 	str	wzr, [sp, #56]
     500:	b90037ff 	str	wzr, [sp, #52]
     504:	b90033ff 	str	wzr, [sp, #48]
     508:	3900bfff 	strb	wzr, [sp, #47]
     50c:	b9003fff 	str	wzr, [sp, #60]
     510:	14000041 	b	614 <rgb888_2_Y1+0x130>
     514:	b9803be0 	ldrsw	x0, [sp, #56]
     518:	f9400fe1 	ldr	x1, [sp, #24]
     51c:	8b000020 	add	x0, x1, x0
     520:	39400000 	ldrb	w0, [x0]
     524:	1e620001 	scvtf	d1, w0
     528:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
     52c:	91000000 	add	x0, x0, #0x0
     530:	fd400000 	ldr	d0, [x0]
     534:	1e600821 	fmul	d1, d1, d0
     538:	b9803be0 	ldrsw	x0, [sp, #56]
     53c:	91000400 	add	x0, x0, #0x1
     540:	f9400fe1 	ldr	x1, [sp, #24]
     544:	8b000020 	add	x0, x1, x0
     548:	39400000 	ldrb	w0, [x0]
     54c:	1e620002 	scvtf	d2, w0
     550:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
     554:	91000000 	add	x0, x0, #0x0
     558:	fd400000 	ldr	d0, [x0]
     55c:	1e600840 	fmul	d0, d2, d0
     560:	1e602821 	fadd	d1, d1, d0
     564:	b9803be0 	ldrsw	x0, [sp, #56]
     568:	91000800 	add	x0, x0, #0x2
     56c:	f9400fe1 	ldr	x1, [sp, #24]
     570:	8b000020 	add	x0, x1, x0
     574:	39400000 	ldrb	w0, [x0]
     578:	1e620002 	scvtf	d2, w0
     57c:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
     580:	91000000 	add	x0, x0, #0x0
     584:	fd400000 	ldr	d0, [x0]
     588:	1e600840 	fmul	d0, d2, d0
     58c:	1e602820 	fadd	d0, d1, d0
     590:	1e790000 	fcvtzu	w0, d0
     594:	3900bbe0 	strb	w0, [sp, #46]
     598:	b9403be0 	ldr	w0, [sp, #56]
     59c:	11000c00 	add	w0, w0, #0x3
     5a0:	b9003be0 	str	w0, [sp, #56]
     5a4:	3940bfe0 	ldrb	w0, [sp, #47]
     5a8:	53017c00 	lsr	w0, w0, #1
     5ac:	3900bfe0 	strb	w0, [sp, #47]
     5b0:	3940bbe0 	ldrb	w0, [sp, #46]
     5b4:	7102001f 	cmp	w0, #0x80
     5b8:	54000089 	b.ls	5c8 <rgb888_2_Y1+0xe4>  // b.plast
     5bc:	3940bfe0 	ldrb	w0, [sp, #47]
     5c0:	32196000 	orr	w0, w0, #0xffffff80
     5c4:	3900bfe0 	strb	w0, [sp, #47]
     5c8:	b94033e0 	ldr	w0, [sp, #48]
     5cc:	11000400 	add	w0, w0, #0x1
     5d0:	b90033e0 	str	w0, [sp, #48]
     5d4:	b94033e0 	ldr	w0, [sp, #48]
     5d8:	7100201f 	cmp	w0, #0x8
     5dc:	54000161 	b.ne	608 <rgb888_2_Y1+0x124>  // b.any
     5e0:	b94037e0 	ldr	w0, [sp, #52]
     5e4:	11000401 	add	w1, w0, #0x1
     5e8:	b90037e1 	str	w1, [sp, #52]
     5ec:	93407c00 	sxtw	x0, w0
     5f0:	f9400be1 	ldr	x1, [sp, #16]
     5f4:	8b000020 	add	x0, x1, x0
     5f8:	3940bfe1 	ldrb	w1, [sp, #47]
     5fc:	39000001 	strb	w1, [x0]
     600:	3900bfff 	strb	wzr, [sp, #47]
     604:	b90033ff 	str	wzr, [sp, #48]
     608:	b9403fe0 	ldr	w0, [sp, #60]
     60c:	11000400 	add	w0, w0, #0x1
     610:	b9003fe0 	str	w0, [sp, #60]
     614:	b9400fe1 	ldr	w1, [sp, #12]
     618:	b9400be0 	ldr	w0, [sp, #8]
     61c:	1b007c20 	mul	w0, w1, w0
     620:	b9403fe1 	ldr	w1, [sp, #60]
     624:	6b00003f 	cmp	w1, w0
     628:	54fff76b 	b.lt	514 <rgb888_2_Y1+0x30>  // b.tstop
     62c:	d503201f 	nop
     630:	910103ff 	add	sp, sp, #0x40
     634:	d65f03c0 	ret

0000000000000638 <kaleido_s1_transfer_ec253tt1>:
     638:	a9b77bfd 	stp	x29, x30, [sp, #-144]!
     63c:	910003fd 	mov	x29, sp
     640:	f90017a0 	str	x0, [x29, #40]
     644:	f90013a1 	str	x1, [x29, #32]
     648:	b9001fa2 	str	w2, [x29, #28]
     64c:	b9001ba3 	str	w3, [x29, #24]
     650:	b90017a4 	str	w4, [x29, #20]
     654:	b90013a5 	str	w5, [x29, #16]
     658:	1e201000 	fmov	s0, #2.000000000000000000e+00
     65c:	bd006fa0 	str	s0, [x29, #108]
     660:	b9008fbf 	str	wzr, [x29, #140]
     664:	1400013c 	b	b54 <kaleido_s1_transfer_ec253tt1+0x51c>
     668:	b9808fa0 	ldrsw	x0, [x29, #140]
     66c:	f94017a1 	ldr	x1, [x29, #40]
     670:	8b000020 	add	x0, x1, x0
     674:	39400000 	ldrb	w0, [x0]
     678:	1e220000 	scvtf	s0, w0
     67c:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
     680:	1e270001 	fmov	s1, w0
     684:	1e211800 	fdiv	s0, s0, s1
     688:	bd0063a0 	str	s0, [x29, #96]
     68c:	b9808fa0 	ldrsw	x0, [x29, #140]
     690:	91000400 	add	x0, x0, #0x1
     694:	f94017a1 	ldr	x1, [x29, #40]
     698:	8b000020 	add	x0, x1, x0
     69c:	39400000 	ldrb	w0, [x0]
     6a0:	1e220000 	scvtf	s0, w0
     6a4:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
     6a8:	1e270001 	fmov	s1, w0
     6ac:	1e211800 	fdiv	s0, s0, s1
     6b0:	bd005fa0 	str	s0, [x29, #92]
     6b4:	b9808fa0 	ldrsw	x0, [x29, #140]
     6b8:	91000800 	add	x0, x0, #0x2
     6bc:	f94017a1 	ldr	x1, [x29, #40]
     6c0:	8b000020 	add	x0, x1, x0
     6c4:	39400000 	ldrb	w0, [x0]
     6c8:	1e220000 	scvtf	s0, w0
     6cc:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
     6d0:	1e270001 	fmov	s1, w0
     6d4:	1e211800 	fdiv	s0, s0, s1
     6d8:	bd005ba0 	str	s0, [x29, #88]
     6dc:	bd4063a1 	ldr	s1, [x29, #96]
     6e0:	bd405fa0 	ldr	s0, [x29, #92]
     6e4:	94000000 	bl	0 <fmaxf>
     6e8:	1e204001 	fmov	s1, s0
     6ec:	bd405ba0 	ldr	s0, [x29, #88]
     6f0:	94000000 	bl	0 <fmaxf>
     6f4:	bd0057a0 	str	s0, [x29, #84]
     6f8:	bd4063a1 	ldr	s1, [x29, #96]
     6fc:	bd405fa0 	ldr	s0, [x29, #92]
     700:	94000000 	bl	0 <fminf>
     704:	1e204001 	fmov	s1, s0
     708:	bd405ba0 	ldr	s0, [x29, #88]
     70c:	94000000 	bl	0 <fminf>
     710:	bd0053a0 	str	s0, [x29, #80]
     714:	bd4057a1 	ldr	s1, [x29, #84]
     718:	bd4053a0 	ldr	s0, [x29, #80]
     71c:	1e203820 	fsub	s0, s1, s0
     720:	bd004fa0 	str	s0, [x29, #76]
     724:	b9008bbf 	str	wzr, [x29, #136]
     728:	bd4057a0 	ldr	s0, [x29, #84]
     72c:	bd004ba0 	str	s0, [x29, #72]
     730:	bd404fa0 	ldr	s0, [x29, #76]
     734:	5296e2e0 	mov	w0, #0xb717                	// #46871
     738:	72a71a20 	movk	w0, #0x38d1, lsl #16
     73c:	1e270001 	fmov	s1, w0
     740:	1e212010 	fcmpe	s0, s1
     744:	5400074d 	b.le	82c <kaleido_s1_transfer_ec253tt1+0x1f4>
     748:	bd4057a0 	ldr	s0, [x29, #84]
     74c:	bd404fa1 	ldr	s1, [x29, #76]
     750:	1e201820 	fdiv	s0, s1, s0
     754:	bd0087a0 	str	s0, [x29, #132]
     758:	bd4057a1 	ldr	s1, [x29, #84]
     75c:	bd405ba0 	ldr	s0, [x29, #88]
     760:	1e202020 	fcmp	s1, s0
     764:	540001c1 	b.ne	79c <kaleido_s1_transfer_ec253tt1+0x164>  // b.any
     768:	bd405fa1 	ldr	s1, [x29, #92]
     76c:	bd4063a0 	ldr	s0, [x29, #96]
     770:	1e203821 	fsub	s1, s1, s0
     774:	bd404fa0 	ldr	s0, [x29, #76]
     778:	1e201820 	fdiv	s0, s1, s0
     77c:	1e231001 	fmov	s1, #6.000000000000000000e+00
     780:	94000000 	bl	0 <fmodf>
     784:	1e204001 	fmov	s1, s0
     788:	52a84e00 	mov	w0, #0x42700000            	// #1114636288
     78c:	1e270000 	fmov	s0, w0
     790:	1e200820 	fmul	s0, s1, s0
     794:	bd008ba0 	str	s0, [x29, #136]
     798:	1400001c 	b	808 <kaleido_s1_transfer_ec253tt1+0x1d0>
     79c:	bd4057a1 	ldr	s1, [x29, #84]
     7a0:	bd405fa0 	ldr	s0, [x29, #92]
     7a4:	1e202020 	fcmp	s1, s0
     7a8:	540001a1 	b.ne	7dc <kaleido_s1_transfer_ec253tt1+0x1a4>  // b.any
     7ac:	bd4063a1 	ldr	s1, [x29, #96]
     7b0:	bd405ba0 	ldr	s0, [x29, #88]
     7b4:	1e203821 	fsub	s1, s1, s0
     7b8:	bd404fa0 	ldr	s0, [x29, #76]
     7bc:	1e201821 	fdiv	s1, s1, s0
     7c0:	1e201000 	fmov	s0, #2.000000000000000000e+00
     7c4:	1e202820 	fadd	s0, s1, s0
     7c8:	52a84e00 	mov	w0, #0x42700000            	// #1114636288
     7cc:	1e270001 	fmov	s1, w0
     7d0:	1e210800 	fmul	s0, s0, s1
     7d4:	bd008ba0 	str	s0, [x29, #136]
     7d8:	1400000c 	b	808 <kaleido_s1_transfer_ec253tt1+0x1d0>
     7dc:	bd405ba1 	ldr	s1, [x29, #88]
     7e0:	bd405fa0 	ldr	s0, [x29, #92]
     7e4:	1e203821 	fsub	s1, s1, s0
     7e8:	bd404fa0 	ldr	s0, [x29, #76]
     7ec:	1e201821 	fdiv	s1, s1, s0
     7f0:	1e221000 	fmov	s0, #4.000000000000000000e+00
     7f4:	1e202820 	fadd	s0, s1, s0
     7f8:	52a84e00 	mov	w0, #0x42700000            	// #1114636288
     7fc:	1e270001 	fmov	s1, w0
     800:	1e210800 	fmul	s0, s0, s1
     804:	bd008ba0 	str	s0, [x29, #136]
     808:	bd408ba0 	ldr	s0, [x29, #136]
     80c:	1e202018 	fcmpe	s0, #0.0
     810:	54000125 	b.pl	834 <kaleido_s1_transfer_ec253tt1+0x1fc>  // b.nfrst
     814:	bd408ba0 	ldr	s0, [x29, #136]
     818:	52a87680 	mov	w0, #0x43b40000            	// #1135869952
     81c:	1e270001 	fmov	s1, w0
     820:	1e212800 	fadd	s0, s0, s1
     824:	bd008ba0 	str	s0, [x29, #136]
     828:	14000003 	b	834 <kaleido_s1_transfer_ec253tt1+0x1fc>
     82c:	b90087bf 	str	wzr, [x29, #132]
     830:	b9008bbf 	str	wzr, [x29, #136]
     834:	bd4087a1 	ldr	s1, [x29, #132]
     838:	bd406fa0 	ldr	s0, [x29, #108]
     83c:	1e200820 	fmul	s0, s1, s0
     840:	bd0087a0 	str	s0, [x29, #132]
     844:	0f000401 	movi	v1.2s, #0x0
     848:	bd4087a0 	ldr	s0, [x29, #132]
     84c:	94000000 	bl	0 <fmaxf>
     850:	1e2e1001 	fmov	s1, #1.000000000000000000e+00
     854:	94000000 	bl	0 <fminf>
     858:	bd0087a0 	str	s0, [x29, #132]
     85c:	bd404ba1 	ldr	s1, [x29, #72]
     860:	bd4087a0 	ldr	s0, [x29, #132]
     864:	1e200820 	fmul	s0, s1, s0
     868:	bd0047a0 	str	s0, [x29, #68]
     86c:	52a84e00 	mov	w0, #0x42700000            	// #1114636288
     870:	1e270001 	fmov	s1, w0
     874:	bd408ba0 	ldr	s0, [x29, #136]
     878:	1e211800 	fdiv	s0, s0, s1
     87c:	1e201001 	fmov	s1, #2.000000000000000000e+00
     880:	94000000 	bl	0 <fmodf>
     884:	1e204001 	fmov	s1, s0
     888:	1e2e1000 	fmov	s0, #1.000000000000000000e+00
     88c:	1e203820 	fsub	s0, s1, s0
     890:	1e20c000 	fabs	s0, s0
     894:	1e2e1001 	fmov	s1, #1.000000000000000000e+00
     898:	1e203820 	fsub	s0, s1, s0
     89c:	bd4047a1 	ldr	s1, [x29, #68]
     8a0:	1e200820 	fmul	s0, s1, s0
     8a4:	bd0043a0 	str	s0, [x29, #64]
     8a8:	bd404ba1 	ldr	s1, [x29, #72]
     8ac:	bd4047a0 	ldr	s0, [x29, #68]
     8b0:	1e203820 	fsub	s0, s1, s0
     8b4:	bd003fa0 	str	s0, [x29, #60]
     8b8:	bd408ba0 	ldr	s0, [x29, #136]
     8bc:	52a84e00 	mov	w0, #0x42700000            	// #1114636288
     8c0:	1e270001 	fmov	s1, w0
     8c4:	1e212010 	fcmpe	s0, s1
     8c8:	540000e5 	b.pl	8e4 <kaleido_s1_transfer_ec253tt1+0x2ac>  // b.nfrst
     8cc:	bd4047a0 	ldr	s0, [x29, #68]
     8d0:	bd0083a0 	str	s0, [x29, #128]
     8d4:	bd4043a0 	ldr	s0, [x29, #64]
     8d8:	bd007fa0 	str	s0, [x29, #124]
     8dc:	b9007bbf 	str	wzr, [x29, #120]
     8e0:	14000032 	b	9a8 <kaleido_s1_transfer_ec253tt1+0x370>
     8e4:	bd408ba0 	ldr	s0, [x29, #136]
     8e8:	52a85e00 	mov	w0, #0x42f00000            	// #1123024896
     8ec:	1e270001 	fmov	s1, w0
     8f0:	1e212010 	fcmpe	s0, s1
     8f4:	540000e5 	b.pl	910 <kaleido_s1_transfer_ec253tt1+0x2d8>  // b.nfrst
     8f8:	bd4043a0 	ldr	s0, [x29, #64]
     8fc:	bd0083a0 	str	s0, [x29, #128]
     900:	bd4047a0 	ldr	s0, [x29, #68]
     904:	bd007fa0 	str	s0, [x29, #124]
     908:	b9007bbf 	str	wzr, [x29, #120]
     90c:	14000027 	b	9a8 <kaleido_s1_transfer_ec253tt1+0x370>
     910:	bd408ba0 	ldr	s0, [x29, #136]
     914:	52a86680 	mov	w0, #0x43340000            	// #1127481344
     918:	1e270001 	fmov	s1, w0
     91c:	1e212010 	fcmpe	s0, s1
     920:	540000e5 	b.pl	93c <kaleido_s1_transfer_ec253tt1+0x304>  // b.nfrst
     924:	b90083bf 	str	wzr, [x29, #128]
     928:	bd4047a0 	ldr	s0, [x29, #68]
     92c:	bd007fa0 	str	s0, [x29, #124]
     930:	bd4043a0 	ldr	s0, [x29, #64]
     934:	bd007ba0 	str	s0, [x29, #120]
     938:	1400001c 	b	9a8 <kaleido_s1_transfer_ec253tt1+0x370>
     93c:	bd408ba0 	ldr	s0, [x29, #136]
     940:	52a86e00 	mov	w0, #0x43700000            	// #1131413504
     944:	1e270001 	fmov	s1, w0
     948:	1e212010 	fcmpe	s0, s1
     94c:	540000e5 	b.pl	968 <kaleido_s1_transfer_ec253tt1+0x330>  // b.nfrst
     950:	b90083bf 	str	wzr, [x29, #128]
     954:	bd4043a0 	ldr	s0, [x29, #64]
     958:	bd007fa0 	str	s0, [x29, #124]
     95c:	bd4047a0 	ldr	s0, [x29, #68]
     960:	bd007ba0 	str	s0, [x29, #120]
     964:	14000011 	b	9a8 <kaleido_s1_transfer_ec253tt1+0x370>
     968:	bd408ba0 	ldr	s0, [x29, #136]
     96c:	52a872c0 	mov	w0, #0x43960000            	// #1133903872
     970:	1e270001 	fmov	s1, w0
     974:	1e212010 	fcmpe	s0, s1
     978:	540000e5 	b.pl	994 <kaleido_s1_transfer_ec253tt1+0x35c>  // b.nfrst
     97c:	bd4043a0 	ldr	s0, [x29, #64]
     980:	bd0083a0 	str	s0, [x29, #128]
     984:	b9007fbf 	str	wzr, [x29, #124]
     988:	bd4047a0 	ldr	s0, [x29, #68]
     98c:	bd007ba0 	str	s0, [x29, #120]
     990:	14000006 	b	9a8 <kaleido_s1_transfer_ec253tt1+0x370>
     994:	bd4047a0 	ldr	s0, [x29, #68]
     998:	bd0083a0 	str	s0, [x29, #128]
     99c:	b9007fbf 	str	wzr, [x29, #124]
     9a0:	bd4043a0 	ldr	s0, [x29, #64]
     9a4:	bd007ba0 	str	s0, [x29, #120]
     9a8:	bd407ba1 	ldr	s1, [x29, #120]
     9ac:	bd403fa0 	ldr	s0, [x29, #60]
     9b0:	1e202820 	fadd	s0, s1, s0
     9b4:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
     9b8:	1e270001 	fmov	s1, w0
     9bc:	1e210800 	fmul	s0, s0, s1
     9c0:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
     9c4:	1e270001 	fmov	s1, w0
     9c8:	1e212010 	fcmpe	s0, s1
     9cc:	5400006d 	b.le	9d8 <kaleido_s1_transfer_ec253tt1+0x3a0>
     9d0:	52801fe0 	mov	w0, #0xff                  	// #255
     9d4:	14000013 	b	a20 <kaleido_s1_transfer_ec253tt1+0x3e8>
     9d8:	bd407ba1 	ldr	s1, [x29, #120]
     9dc:	bd403fa0 	ldr	s0, [x29, #60]
     9e0:	1e202820 	fadd	s0, s1, s0
     9e4:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
     9e8:	1e270001 	fmov	s1, w0
     9ec:	1e210800 	fmul	s0, s0, s1
     9f0:	1e202018 	fcmpe	s0, #0.0
     9f4:	54000065 	b.pl	a00 <kaleido_s1_transfer_ec253tt1+0x3c8>  // b.nfrst
     9f8:	52800000 	mov	w0, #0x0                   	// #0
     9fc:	14000009 	b	a20 <kaleido_s1_transfer_ec253tt1+0x3e8>
     a00:	bd407ba1 	ldr	s1, [x29, #120]
     a04:	bd403fa0 	ldr	s0, [x29, #60]
     a08:	1e202820 	fadd	s0, s1, s0
     a0c:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
     a10:	1e270001 	fmov	s1, w0
     a14:	1e210800 	fmul	s0, s0, s1
     a18:	1e390000 	fcvtzu	w0, s0
     a1c:	12001c00 	and	w0, w0, #0xff
     a20:	b9808fa1 	ldrsw	x1, [x29, #140]
     a24:	f94017a2 	ldr	x2, [x29, #40]
     a28:	8b010041 	add	x1, x2, x1
     a2c:	39000020 	strb	w0, [x1]
     a30:	bd407fa1 	ldr	s1, [x29, #124]
     a34:	bd403fa0 	ldr	s0, [x29, #60]
     a38:	1e202820 	fadd	s0, s1, s0
     a3c:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
     a40:	1e270001 	fmov	s1, w0
     a44:	1e210800 	fmul	s0, s0, s1
     a48:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
     a4c:	1e270001 	fmov	s1, w0
     a50:	1e212010 	fcmpe	s0, s1
     a54:	5400006d 	b.le	a60 <kaleido_s1_transfer_ec253tt1+0x428>
     a58:	52801fe0 	mov	w0, #0xff                  	// #255
     a5c:	14000013 	b	aa8 <kaleido_s1_transfer_ec253tt1+0x470>
     a60:	bd407fa1 	ldr	s1, [x29, #124]
     a64:	bd403fa0 	ldr	s0, [x29, #60]
     a68:	1e202820 	fadd	s0, s1, s0
     a6c:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
     a70:	1e270001 	fmov	s1, w0
     a74:	1e210800 	fmul	s0, s0, s1
     a78:	1e202018 	fcmpe	s0, #0.0
     a7c:	54000065 	b.pl	a88 <kaleido_s1_transfer_ec253tt1+0x450>  // b.nfrst
     a80:	52800000 	mov	w0, #0x0                   	// #0
     a84:	14000009 	b	aa8 <kaleido_s1_transfer_ec253tt1+0x470>
     a88:	bd407fa1 	ldr	s1, [x29, #124]
     a8c:	bd403fa0 	ldr	s0, [x29, #60]
     a90:	1e202820 	fadd	s0, s1, s0
     a94:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
     a98:	1e270001 	fmov	s1, w0
     a9c:	1e210800 	fmul	s0, s0, s1
     aa0:	1e390000 	fcvtzu	w0, s0
     aa4:	12001c00 	and	w0, w0, #0xff
     aa8:	b9808fa1 	ldrsw	x1, [x29, #140]
     aac:	91000421 	add	x1, x1, #0x1
     ab0:	f94017a2 	ldr	x2, [x29, #40]
     ab4:	8b010041 	add	x1, x2, x1
     ab8:	39000020 	strb	w0, [x1]
     abc:	bd4083a1 	ldr	s1, [x29, #128]
     ac0:	bd403fa0 	ldr	s0, [x29, #60]
     ac4:	1e202820 	fadd	s0, s1, s0
     ac8:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
     acc:	1e270001 	fmov	s1, w0
     ad0:	1e210800 	fmul	s0, s0, s1
     ad4:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
     ad8:	1e270001 	fmov	s1, w0
     adc:	1e212010 	fcmpe	s0, s1
     ae0:	5400006d 	b.le	aec <kaleido_s1_transfer_ec253tt1+0x4b4>
     ae4:	52801fe0 	mov	w0, #0xff                  	// #255
     ae8:	14000013 	b	b34 <kaleido_s1_transfer_ec253tt1+0x4fc>
     aec:	bd4083a1 	ldr	s1, [x29, #128]
     af0:	bd403fa0 	ldr	s0, [x29, #60]
     af4:	1e202820 	fadd	s0, s1, s0
     af8:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
     afc:	1e270001 	fmov	s1, w0
     b00:	1e210800 	fmul	s0, s0, s1
     b04:	1e202018 	fcmpe	s0, #0.0
     b08:	54000065 	b.pl	b14 <kaleido_s1_transfer_ec253tt1+0x4dc>  // b.nfrst
     b0c:	52800000 	mov	w0, #0x0                   	// #0
     b10:	14000009 	b	b34 <kaleido_s1_transfer_ec253tt1+0x4fc>
     b14:	bd4083a1 	ldr	s1, [x29, #128]
     b18:	bd403fa0 	ldr	s0, [x29, #60]
     b1c:	1e202820 	fadd	s0, s1, s0
     b20:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
     b24:	1e270001 	fmov	s1, w0
     b28:	1e210800 	fmul	s0, s0, s1
     b2c:	1e390000 	fcvtzu	w0, s0
     b30:	12001c00 	and	w0, w0, #0xff
     b34:	b9808fa1 	ldrsw	x1, [x29, #140]
     b38:	91000821 	add	x1, x1, #0x2
     b3c:	f94017a2 	ldr	x2, [x29, #40]
     b40:	8b010041 	add	x1, x2, x1
     b44:	39000020 	strb	w0, [x1]
     b48:	b9408fa0 	ldr	w0, [x29, #140]
     b4c:	11000c00 	add	w0, w0, #0x3
     b50:	b9008fa0 	str	w0, [x29, #140]
     b54:	b94017a1 	ldr	w1, [x29, #20]
     b58:	b94013a0 	ldr	w0, [x29, #16]
     b5c:	1b007c21 	mul	w1, w1, w0
     b60:	2a0103e0 	mov	w0, w1
     b64:	531f7800 	lsl	w0, w0, #1
     b68:	0b010000 	add	w0, w0, w1
     b6c:	b9408fa1 	ldr	w1, [x29, #140]
     b70:	6b00003f 	cmp	w1, w0
     b74:	54ffd7ab 	b.lt	668 <kaleido_s1_transfer_ec253tt1+0x30>  // b.tstop
     b78:	b90077bf 	str	wzr, [x29, #116]
     b7c:	14000044 	b	c8c <kaleido_s1_transfer_ec253tt1+0x654>
     b80:	b94077a1 	ldr	w1, [x29, #116]
     b84:	b9401ba0 	ldr	w0, [x29, #24]
     b88:	0b000022 	add	w2, w1, w0
     b8c:	528aaac0 	mov	w0, #0x5556                	// #21846
     b90:	72aaaaa0 	movk	w0, #0x5555, lsl #16
     b94:	9b207c40 	smull	x0, w2, w0
     b98:	d360fc01 	lsr	x1, x0, #32
     b9c:	131f7c40 	asr	w0, w2, #31
     ba0:	4b000021 	sub	w1, w1, w0
     ba4:	2a0103e0 	mov	w0, w1
     ba8:	531f7800 	lsl	w0, w0, #1
     bac:	0b010000 	add	w0, w0, w1
     bb0:	4b000041 	sub	w1, w2, w0
     bb4:	52800040 	mov	w0, #0x2                   	// #2
     bb8:	4b010000 	sub	w0, w0, w1
     bbc:	b9006ba0 	str	w0, [x29, #104]
     bc0:	b90073bf 	str	wzr, [x29, #112]
     bc4:	1400002b 	b	c70 <kaleido_s1_transfer_ec253tt1+0x638>
     bc8:	b9406ba1 	ldr	w1, [x29, #104]
     bcc:	b94073a0 	ldr	w0, [x29, #112]
     bd0:	0b000021 	add	w1, w1, w0
     bd4:	b9401fa0 	ldr	w0, [x29, #28]
     bd8:	0b000021 	add	w1, w1, w0
     bdc:	528aaac0 	mov	w0, #0x5556                	// #21846
     be0:	72aaaaa0 	movk	w0, #0x5555, lsl #16
     be4:	9b207c20 	smull	x0, w1, w0
     be8:	d360fc02 	lsr	x2, x0, #32
     bec:	131f7c20 	asr	w0, w1, #31
     bf0:	4b000042 	sub	w2, w2, w0
     bf4:	2a0203e0 	mov	w0, w2
     bf8:	531f7800 	lsl	w0, w0, #1
     bfc:	0b020000 	add	w0, w0, w2
     c00:	4b000020 	sub	w0, w1, w0
     c04:	b90067a0 	str	w0, [x29, #100]
     c08:	b94077a1 	ldr	w1, [x29, #116]
     c0c:	b94017a0 	ldr	w0, [x29, #20]
     c10:	1b007c21 	mul	w1, w1, w0
     c14:	b94073a0 	ldr	w0, [x29, #112]
     c18:	0b000021 	add	w1, w1, w0
     c1c:	2a0103e0 	mov	w0, w1
     c20:	531f7800 	lsl	w0, w0, #1
     c24:	0b010001 	add	w1, w0, w1
     c28:	b94067a0 	ldr	w0, [x29, #100]
     c2c:	0b000020 	add	w0, w1, w0
     c30:	93407c00 	sxtw	x0, w0
     c34:	f94017a1 	ldr	x1, [x29, #40]
     c38:	8b000021 	add	x1, x1, x0
     c3c:	b94077a2 	ldr	w2, [x29, #116]
     c40:	b94017a0 	ldr	w0, [x29, #20]
     c44:	1b007c42 	mul	w2, w2, w0
     c48:	b94073a0 	ldr	w0, [x29, #112]
     c4c:	0b000040 	add	w0, w2, w0
     c50:	93407c00 	sxtw	x0, w0
     c54:	f94013a2 	ldr	x2, [x29, #32]
     c58:	8b000040 	add	x0, x2, x0
     c5c:	39400021 	ldrb	w1, [x1]
     c60:	39000001 	strb	w1, [x0]
     c64:	b94073a0 	ldr	w0, [x29, #112]
     c68:	11000400 	add	w0, w0, #0x1
     c6c:	b90073a0 	str	w0, [x29, #112]
     c70:	b94073a1 	ldr	w1, [x29, #112]
     c74:	b94017a0 	ldr	w0, [x29, #20]
     c78:	6b00003f 	cmp	w1, w0
     c7c:	54fffa6b 	b.lt	bc8 <kaleido_s1_transfer_ec253tt1+0x590>  // b.tstop
     c80:	b94077a0 	ldr	w0, [x29, #116]
     c84:	11000400 	add	w0, w0, #0x1
     c88:	b90077a0 	str	w0, [x29, #116]
     c8c:	b94077a1 	ldr	w1, [x29, #116]
     c90:	b94013a0 	ldr	w0, [x29, #16]
     c94:	6b00003f 	cmp	w1, w0
     c98:	54fff74b 	b.lt	b80 <kaleido_s1_transfer_ec253tt1+0x548>  // b.tstop
     c9c:	d503201f 	nop
     ca0:	a8c97bfd 	ldp	x29, x30, [sp], #144
     ca4:	d65f03c0 	ret

0000000000000ca8 <adjustColor>:
     ca8:	a9b97bfd 	stp	x29, x30, [sp, #-112]!
     cac:	910003fd 	mov	x29, sp
     cb0:	f90017a0 	str	x0, [x29, #40]
     cb4:	bd0027a0 	str	s0, [x29, #36]
     cb8:	bd0023a1 	str	s1, [x29, #32]
     cbc:	bd001fa2 	str	s2, [x29, #28]
     cc0:	bd001ba3 	str	s3, [x29, #24]
     cc4:	f94017a0 	ldr	x0, [x29, #40]
     cc8:	39400000 	ldrb	w0, [x0]
     ccc:	1e220000 	scvtf	s0, w0
     cd0:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
     cd4:	1e270001 	fmov	s1, w0
     cd8:	1e211800 	fdiv	s0, s0, s1
     cdc:	bd005ba0 	str	s0, [x29, #88]
     ce0:	f94017a0 	ldr	x0, [x29, #40]
     ce4:	91000400 	add	x0, x0, #0x1
     ce8:	39400000 	ldrb	w0, [x0]
     cec:	1e220000 	scvtf	s0, w0
     cf0:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
     cf4:	1e270001 	fmov	s1, w0
     cf8:	1e211800 	fdiv	s0, s0, s1
     cfc:	bd0057a0 	str	s0, [x29, #84]
     d00:	f94017a0 	ldr	x0, [x29, #40]
     d04:	91000800 	add	x0, x0, #0x2
     d08:	39400000 	ldrb	w0, [x0]
     d0c:	1e220000 	scvtf	s0, w0
     d10:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
     d14:	1e270001 	fmov	s1, w0
     d18:	1e211800 	fdiv	s0, s0, s1
     d1c:	bd0053a0 	str	s0, [x29, #80]
     d20:	bd4053a1 	ldr	s1, [x29, #80]
     d24:	bd4057a0 	ldr	s0, [x29, #84]
     d28:	94000000 	bl	0 <fmaxf>
     d2c:	1e204001 	fmov	s1, s0
     d30:	bd405ba0 	ldr	s0, [x29, #88]
     d34:	94000000 	bl	0 <fmaxf>
     d38:	bd004fa0 	str	s0, [x29, #76]
     d3c:	bd4053a1 	ldr	s1, [x29, #80]
     d40:	bd4057a0 	ldr	s0, [x29, #84]
     d44:	94000000 	bl	0 <fminf>
     d48:	1e204001 	fmov	s1, s0
     d4c:	bd405ba0 	ldr	s0, [x29, #88]
     d50:	94000000 	bl	0 <fminf>
     d54:	bd004ba0 	str	s0, [x29, #72]
     d58:	bd404fa1 	ldr	s1, [x29, #76]
     d5c:	bd404ba0 	ldr	s0, [x29, #72]
     d60:	1e203820 	fsub	s0, s1, s0
     d64:	bd0047a0 	str	s0, [x29, #68]
     d68:	b9006fbf 	str	wzr, [x29, #108]
     d6c:	bd404fa0 	ldr	s0, [x29, #76]
     d70:	bd0043a0 	str	s0, [x29, #64]
     d74:	bd4047a0 	ldr	s0, [x29, #68]
     d78:	5296e2e0 	mov	w0, #0xb717                	// #46871
     d7c:	72a71a20 	movk	w0, #0x38d1, lsl #16
     d80:	1e270001 	fmov	s1, w0
     d84:	1e212010 	fcmpe	s0, s1
     d88:	5400074d 	b.le	e70 <adjustColor+0x1c8>
     d8c:	bd404fa0 	ldr	s0, [x29, #76]
     d90:	bd4047a1 	ldr	s1, [x29, #68]
     d94:	1e201820 	fdiv	s0, s1, s0
     d98:	bd006ba0 	str	s0, [x29, #104]
     d9c:	bd404fa1 	ldr	s1, [x29, #76]
     da0:	bd405ba0 	ldr	s0, [x29, #88]
     da4:	1e202020 	fcmp	s1, s0
     da8:	540001c1 	b.ne	de0 <adjustColor+0x138>  // b.any
     dac:	bd4057a1 	ldr	s1, [x29, #84]
     db0:	bd4053a0 	ldr	s0, [x29, #80]
     db4:	1e203821 	fsub	s1, s1, s0
     db8:	bd4047a0 	ldr	s0, [x29, #68]
     dbc:	1e201820 	fdiv	s0, s1, s0
     dc0:	1e231001 	fmov	s1, #6.000000000000000000e+00
     dc4:	94000000 	bl	0 <fmodf>
     dc8:	1e204001 	fmov	s1, s0
     dcc:	52a84e00 	mov	w0, #0x42700000            	// #1114636288
     dd0:	1e270000 	fmov	s0, w0
     dd4:	1e200820 	fmul	s0, s1, s0
     dd8:	bd006fa0 	str	s0, [x29, #108]
     ddc:	1400001c 	b	e4c <adjustColor+0x1a4>
     de0:	bd404fa1 	ldr	s1, [x29, #76]
     de4:	bd4057a0 	ldr	s0, [x29, #84]
     de8:	1e202020 	fcmp	s1, s0
     dec:	540001a1 	b.ne	e20 <adjustColor+0x178>  // b.any
     df0:	bd4053a1 	ldr	s1, [x29, #80]
     df4:	bd405ba0 	ldr	s0, [x29, #88]
     df8:	1e203821 	fsub	s1, s1, s0
     dfc:	bd4047a0 	ldr	s0, [x29, #68]
     e00:	1e201821 	fdiv	s1, s1, s0
     e04:	1e201000 	fmov	s0, #2.000000000000000000e+00
     e08:	1e202820 	fadd	s0, s1, s0
     e0c:	52a84e00 	mov	w0, #0x42700000            	// #1114636288
     e10:	1e270001 	fmov	s1, w0
     e14:	1e210800 	fmul	s0, s0, s1
     e18:	bd006fa0 	str	s0, [x29, #108]
     e1c:	1400000c 	b	e4c <adjustColor+0x1a4>
     e20:	bd405ba1 	ldr	s1, [x29, #88]
     e24:	bd4057a0 	ldr	s0, [x29, #84]
     e28:	1e203821 	fsub	s1, s1, s0
     e2c:	bd4047a0 	ldr	s0, [x29, #68]
     e30:	1e201821 	fdiv	s1, s1, s0
     e34:	1e221000 	fmov	s0, #4.000000000000000000e+00
     e38:	1e202820 	fadd	s0, s1, s0
     e3c:	52a84e00 	mov	w0, #0x42700000            	// #1114636288
     e40:	1e270001 	fmov	s1, w0
     e44:	1e210800 	fmul	s0, s0, s1
     e48:	bd006fa0 	str	s0, [x29, #108]
     e4c:	bd406fa0 	ldr	s0, [x29, #108]
     e50:	1e202018 	fcmpe	s0, #0.0
     e54:	54000125 	b.pl	e78 <adjustColor+0x1d0>  // b.nfrst
     e58:	bd406fa0 	ldr	s0, [x29, #108]
     e5c:	52a87680 	mov	w0, #0x43b40000            	// #1135869952
     e60:	1e270001 	fmov	s1, w0
     e64:	1e212800 	fadd	s0, s0, s1
     e68:	bd006fa0 	str	s0, [x29, #108]
     e6c:	14000003 	b	e78 <adjustColor+0x1d0>
     e70:	b9006bbf 	str	wzr, [x29, #104]
     e74:	b9006fbf 	str	wzr, [x29, #108]
     e78:	bd406ba1 	ldr	s1, [x29, #104]
     e7c:	bd4027a0 	ldr	s0, [x29, #36]
     e80:	1e200820 	fmul	s0, s1, s0
     e84:	bd006ba0 	str	s0, [x29, #104]
     e88:	0f000401 	movi	v1.2s, #0x0
     e8c:	bd406ba0 	ldr	s0, [x29, #104]
     e90:	94000000 	bl	0 <fmaxf>
     e94:	1e2e1001 	fmov	s1, #1.000000000000000000e+00
     e98:	94000000 	bl	0 <fminf>
     e9c:	bd006ba0 	str	s0, [x29, #104]
     ea0:	bd4043a1 	ldr	s1, [x29, #64]
     ea4:	bd406ba0 	ldr	s0, [x29, #104]
     ea8:	1e200820 	fmul	s0, s1, s0
     eac:	bd003fa0 	str	s0, [x29, #60]
     eb0:	52a84e00 	mov	w0, #0x42700000            	// #1114636288
     eb4:	1e270001 	fmov	s1, w0
     eb8:	bd406fa0 	ldr	s0, [x29, #108]
     ebc:	1e211800 	fdiv	s0, s0, s1
     ec0:	1e201001 	fmov	s1, #2.000000000000000000e+00
     ec4:	94000000 	bl	0 <fmodf>
     ec8:	1e204001 	fmov	s1, s0
     ecc:	1e2e1000 	fmov	s0, #1.000000000000000000e+00
     ed0:	1e203820 	fsub	s0, s1, s0
     ed4:	1e20c000 	fabs	s0, s0
     ed8:	1e2e1001 	fmov	s1, #1.000000000000000000e+00
     edc:	1e203820 	fsub	s0, s1, s0
     ee0:	bd403fa1 	ldr	s1, [x29, #60]
     ee4:	1e200820 	fmul	s0, s1, s0
     ee8:	bd003ba0 	str	s0, [x29, #56]
     eec:	bd4043a1 	ldr	s1, [x29, #64]
     ef0:	bd403fa0 	ldr	s0, [x29, #60]
     ef4:	1e203820 	fsub	s0, s1, s0
     ef8:	bd0037a0 	str	s0, [x29, #52]
     efc:	bd406fa0 	ldr	s0, [x29, #108]
     f00:	52a84e00 	mov	w0, #0x42700000            	// #1114636288
     f04:	1e270001 	fmov	s1, w0
     f08:	1e212010 	fcmpe	s0, s1
     f0c:	540000e5 	b.pl	f28 <adjustColor+0x280>  // b.nfrst
     f10:	bd403fa0 	ldr	s0, [x29, #60]
     f14:	bd0067a0 	str	s0, [x29, #100]
     f18:	bd403ba0 	ldr	s0, [x29, #56]
     f1c:	bd0063a0 	str	s0, [x29, #96]
     f20:	b9005fbf 	str	wzr, [x29, #92]
     f24:	14000032 	b	fec <adjustColor+0x344>
     f28:	bd406fa0 	ldr	s0, [x29, #108]
     f2c:	52a85e00 	mov	w0, #0x42f00000            	// #1123024896
     f30:	1e270001 	fmov	s1, w0
     f34:	1e212010 	fcmpe	s0, s1
     f38:	540000e5 	b.pl	f54 <adjustColor+0x2ac>  // b.nfrst
     f3c:	bd403ba0 	ldr	s0, [x29, #56]
     f40:	bd0067a0 	str	s0, [x29, #100]
     f44:	bd403fa0 	ldr	s0, [x29, #60]
     f48:	bd0063a0 	str	s0, [x29, #96]
     f4c:	b9005fbf 	str	wzr, [x29, #92]
     f50:	14000027 	b	fec <adjustColor+0x344>
     f54:	bd406fa0 	ldr	s0, [x29, #108]
     f58:	52a86680 	mov	w0, #0x43340000            	// #1127481344
     f5c:	1e270001 	fmov	s1, w0
     f60:	1e212010 	fcmpe	s0, s1
     f64:	540000e5 	b.pl	f80 <adjustColor+0x2d8>  // b.nfrst
     f68:	b90067bf 	str	wzr, [x29, #100]
     f6c:	bd403fa0 	ldr	s0, [x29, #60]
     f70:	bd0063a0 	str	s0, [x29, #96]
     f74:	bd403ba0 	ldr	s0, [x29, #56]
     f78:	bd005fa0 	str	s0, [x29, #92]
     f7c:	1400001c 	b	fec <adjustColor+0x344>
     f80:	bd406fa0 	ldr	s0, [x29, #108]
     f84:	52a86e00 	mov	w0, #0x43700000            	// #1131413504
     f88:	1e270001 	fmov	s1, w0
     f8c:	1e212010 	fcmpe	s0, s1
     f90:	540000e5 	b.pl	fac <adjustColor+0x304>  // b.nfrst
     f94:	b90067bf 	str	wzr, [x29, #100]
     f98:	bd403ba0 	ldr	s0, [x29, #56]
     f9c:	bd0063a0 	str	s0, [x29, #96]
     fa0:	bd403fa0 	ldr	s0, [x29, #60]
     fa4:	bd005fa0 	str	s0, [x29, #92]
     fa8:	14000011 	b	fec <adjustColor+0x344>
     fac:	bd406fa0 	ldr	s0, [x29, #108]
     fb0:	52a872c0 	mov	w0, #0x43960000            	// #1133903872
     fb4:	1e270001 	fmov	s1, w0
     fb8:	1e212010 	fcmpe	s0, s1
     fbc:	540000e5 	b.pl	fd8 <adjustColor+0x330>  // b.nfrst
     fc0:	bd403ba0 	ldr	s0, [x29, #56]
     fc4:	bd0067a0 	str	s0, [x29, #100]
     fc8:	b90063bf 	str	wzr, [x29, #96]
     fcc:	bd403fa0 	ldr	s0, [x29, #60]
     fd0:	bd005fa0 	str	s0, [x29, #92]
     fd4:	14000006 	b	fec <adjustColor+0x344>
     fd8:	bd403fa0 	ldr	s0, [x29, #60]
     fdc:	bd0067a0 	str	s0, [x29, #100]
     fe0:	b90063bf 	str	wzr, [x29, #96]
     fe4:	bd403ba0 	ldr	s0, [x29, #56]
     fe8:	bd005fa0 	str	s0, [x29, #92]
     fec:	bd4067a1 	ldr	s1, [x29, #100]
     ff0:	bd4037a0 	ldr	s0, [x29, #52]
     ff4:	1e202820 	fadd	s0, s1, s0
     ff8:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
     ffc:	1e270001 	fmov	s1, w0
    1000:	1e210800 	fmul	s0, s0, s1
    1004:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1008:	1e270001 	fmov	s1, w0
    100c:	1e212010 	fcmpe	s0, s1
    1010:	5400006d 	b.le	101c <adjustColor+0x374>
    1014:	52801fe0 	mov	w0, #0xff                  	// #255
    1018:	14000013 	b	1064 <adjustColor+0x3bc>
    101c:	bd4067a1 	ldr	s1, [x29, #100]
    1020:	bd4037a0 	ldr	s0, [x29, #52]
    1024:	1e202820 	fadd	s0, s1, s0
    1028:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    102c:	1e270001 	fmov	s1, w0
    1030:	1e210800 	fmul	s0, s0, s1
    1034:	1e202018 	fcmpe	s0, #0.0
    1038:	54000065 	b.pl	1044 <adjustColor+0x39c>  // b.nfrst
    103c:	52800000 	mov	w0, #0x0                   	// #0
    1040:	14000009 	b	1064 <adjustColor+0x3bc>
    1044:	bd4067a1 	ldr	s1, [x29, #100]
    1048:	bd4037a0 	ldr	s0, [x29, #52]
    104c:	1e202820 	fadd	s0, s1, s0
    1050:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1054:	1e270001 	fmov	s1, w0
    1058:	1e210800 	fmul	s0, s0, s1
    105c:	1e390000 	fcvtzu	w0, s0
    1060:	12001c00 	and	w0, w0, #0xff
    1064:	f94017a1 	ldr	x1, [x29, #40]
    1068:	39000020 	strb	w0, [x1]
    106c:	bd4063a1 	ldr	s1, [x29, #96]
    1070:	bd4037a0 	ldr	s0, [x29, #52]
    1074:	1e202820 	fadd	s0, s1, s0
    1078:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    107c:	1e270001 	fmov	s1, w0
    1080:	1e210800 	fmul	s0, s0, s1
    1084:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1088:	1e270001 	fmov	s1, w0
    108c:	1e212010 	fcmpe	s0, s1
    1090:	5400006d 	b.le	109c <adjustColor+0x3f4>
    1094:	52801fe0 	mov	w0, #0xff                  	// #255
    1098:	14000013 	b	10e4 <adjustColor+0x43c>
    109c:	bd4063a1 	ldr	s1, [x29, #96]
    10a0:	bd4037a0 	ldr	s0, [x29, #52]
    10a4:	1e202820 	fadd	s0, s1, s0
    10a8:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    10ac:	1e270001 	fmov	s1, w0
    10b0:	1e210800 	fmul	s0, s0, s1
    10b4:	1e202018 	fcmpe	s0, #0.0
    10b8:	54000065 	b.pl	10c4 <adjustColor+0x41c>  // b.nfrst
    10bc:	52800000 	mov	w0, #0x0                   	// #0
    10c0:	14000009 	b	10e4 <adjustColor+0x43c>
    10c4:	bd4063a1 	ldr	s1, [x29, #96]
    10c8:	bd4037a0 	ldr	s0, [x29, #52]
    10cc:	1e202820 	fadd	s0, s1, s0
    10d0:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    10d4:	1e270001 	fmov	s1, w0
    10d8:	1e210800 	fmul	s0, s0, s1
    10dc:	1e390000 	fcvtzu	w0, s0
    10e0:	12001c00 	and	w0, w0, #0xff
    10e4:	f94017a1 	ldr	x1, [x29, #40]
    10e8:	91000421 	add	x1, x1, #0x1
    10ec:	39000020 	strb	w0, [x1]
    10f0:	bd405fa1 	ldr	s1, [x29, #92]
    10f4:	bd4037a0 	ldr	s0, [x29, #52]
    10f8:	1e202820 	fadd	s0, s1, s0
    10fc:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1100:	1e270001 	fmov	s1, w0
    1104:	1e210800 	fmul	s0, s0, s1
    1108:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    110c:	1e270001 	fmov	s1, w0
    1110:	1e212010 	fcmpe	s0, s1
    1114:	5400006d 	b.le	1120 <adjustColor+0x478>
    1118:	52801fe0 	mov	w0, #0xff                  	// #255
    111c:	14000013 	b	1168 <adjustColor+0x4c0>
    1120:	bd405fa1 	ldr	s1, [x29, #92]
    1124:	bd4037a0 	ldr	s0, [x29, #52]
    1128:	1e202820 	fadd	s0, s1, s0
    112c:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1130:	1e270001 	fmov	s1, w0
    1134:	1e210800 	fmul	s0, s0, s1
    1138:	1e202018 	fcmpe	s0, #0.0
    113c:	54000065 	b.pl	1148 <adjustColor+0x4a0>  // b.nfrst
    1140:	52800000 	mov	w0, #0x0                   	// #0
    1144:	14000009 	b	1168 <adjustColor+0x4c0>
    1148:	bd405fa1 	ldr	s1, [x29, #92]
    114c:	bd4037a0 	ldr	s0, [x29, #52]
    1150:	1e202820 	fadd	s0, s1, s0
    1154:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1158:	1e270001 	fmov	s1, w0
    115c:	1e210800 	fmul	s0, s0, s1
    1160:	1e390000 	fcvtzu	w0, s0
    1164:	12001c00 	and	w0, w0, #0xff
    1168:	f94017a1 	ldr	x1, [x29, #40]
    116c:	91000821 	add	x1, x1, #0x2
    1170:	39000020 	strb	w0, [x1]
    1174:	d503201f 	nop
    1178:	a8c77bfd 	ldp	x29, x30, [sp], #112
    117c:	d65f03c0 	ret

0000000000001180 <ImageProcess_ColorEnhace>:
    1180:	a9bc7bfd 	stp	x29, x30, [sp, #-64]!
    1184:	910003fd 	mov	x29, sp
    1188:	bd002fa0 	str	s0, [x29, #44]
    118c:	bd002ba1 	str	s1, [x29, #40]
    1190:	bd0027a2 	str	s2, [x29, #36]
    1194:	bd0023a3 	str	s3, [x29, #32]
    1198:	f9000fa0 	str	x0, [x29, #24]
    119c:	b90017a1 	str	w1, [x29, #20]
    11a0:	b90013a2 	str	w2, [x29, #16]
    11a4:	b9003fbf 	str	wzr, [x29, #60]
    11a8:	14000010 	b	11e8 <ImageProcess_ColorEnhace+0x68>
    11ac:	b9403fa1 	ldr	w1, [x29, #60]
    11b0:	2a0103e0 	mov	w0, w1
    11b4:	531f7800 	lsl	w0, w0, #1
    11b8:	0b010000 	add	w0, w0, w1
    11bc:	93407c00 	sxtw	x0, w0
    11c0:	f9400fa1 	ldr	x1, [x29, #24]
    11c4:	8b000020 	add	x0, x1, x0
    11c8:	bd4027a3 	ldr	s3, [x29, #36]
    11cc:	bd4023a2 	ldr	s2, [x29, #32]
    11d0:	bd402ba1 	ldr	s1, [x29, #40]
    11d4:	bd402fa0 	ldr	s0, [x29, #44]
    11d8:	97fffeb4 	bl	ca8 <adjustColor>
    11dc:	b9403fa0 	ldr	w0, [x29, #60]
    11e0:	11000400 	add	w0, w0, #0x1
    11e4:	b9003fa0 	str	w0, [x29, #60]
    11e8:	b94017a1 	ldr	w1, [x29, #20]
    11ec:	b94013a0 	ldr	w0, [x29, #16]
    11f0:	1b007c20 	mul	w0, w1, w0
    11f4:	b9403fa1 	ldr	w1, [x29, #60]
    11f8:	6b00003f 	cmp	w1, w0
    11fc:	54fffd8b 	b.lt	11ac <ImageProcess_ColorEnhace+0x2c>  // b.tstop
    1200:	d503201f 	nop
    1204:	a8c47bfd 	ldp	x29, x30, [sp], #64
    1208:	d65f03c0 	ret

000000000000120c <find_nearest_color_AIO>:
    120c:	d100c3ff 	sub	sp, sp, #0x30
    1210:	b9000fe0 	str	w0, [sp, #12]
    1214:	b9000be1 	str	w1, [sp, #8]
    1218:	b90007e2 	str	w2, [sp, #4]
    121c:	12b00000 	mov	w0, #0x7fffffff            	// #2147483647
    1220:	b9002fe0 	str	w0, [sp, #44]
    1224:	b9002bff 	str	wzr, [sp, #40]
    1228:	b90027ff 	str	wzr, [sp, #36]
    122c:	1400003c 	b	131c <find_nearest_color_AIO+0x110>
    1230:	b9400fe0 	ldr	w0, [sp, #12]
    1234:	12001c02 	and	w2, w0, #0xff
    1238:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    123c:	91000003 	add	x3, x0, #0x0
    1240:	b98027e1 	ldrsw	x1, [sp, #36]
    1244:	aa0103e0 	mov	x0, x1
    1248:	d37ff800 	lsl	x0, x0, #1
    124c:	8b010000 	add	x0, x0, x1
    1250:	8b000060 	add	x0, x3, x0
    1254:	39400000 	ldrb	w0, [x0]
    1258:	4b000040 	sub	w0, w2, w0
    125c:	b90023e0 	str	w0, [sp, #32]
    1260:	b9400be0 	ldr	w0, [sp, #8]
    1264:	12001c02 	and	w2, w0, #0xff
    1268:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    126c:	91000003 	add	x3, x0, #0x0
    1270:	b98027e1 	ldrsw	x1, [sp, #36]
    1274:	aa0103e0 	mov	x0, x1
    1278:	d37ff800 	lsl	x0, x0, #1
    127c:	8b010000 	add	x0, x0, x1
    1280:	8b000060 	add	x0, x3, x0
    1284:	39400400 	ldrb	w0, [x0, #1]
    1288:	4b000040 	sub	w0, w2, w0
    128c:	b9001fe0 	str	w0, [sp, #28]
    1290:	b94007e0 	ldr	w0, [sp, #4]
    1294:	12001c02 	and	w2, w0, #0xff
    1298:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    129c:	91000003 	add	x3, x0, #0x0
    12a0:	b98027e1 	ldrsw	x1, [sp, #36]
    12a4:	aa0103e0 	mov	x0, x1
    12a8:	d37ff800 	lsl	x0, x0, #1
    12ac:	8b010000 	add	x0, x0, x1
    12b0:	8b000060 	add	x0, x3, x0
    12b4:	39400800 	ldrb	w0, [x0, #2]
    12b8:	4b000040 	sub	w0, w2, w0
    12bc:	b9001be0 	str	w0, [sp, #24]
    12c0:	b94023e1 	ldr	w1, [sp, #32]
    12c4:	b94023e0 	ldr	w0, [sp, #32]
    12c8:	1b007c21 	mul	w1, w1, w0
    12cc:	b9401fe2 	ldr	w2, [sp, #28]
    12d0:	b9401fe0 	ldr	w0, [sp, #28]
    12d4:	1b007c40 	mul	w0, w2, w0
    12d8:	0b000021 	add	w1, w1, w0
    12dc:	b9401be2 	ldr	w2, [sp, #24]
    12e0:	b9401be0 	ldr	w0, [sp, #24]
    12e4:	1b007c40 	mul	w0, w2, w0
    12e8:	0b000020 	add	w0, w1, w0
    12ec:	b90017e0 	str	w0, [sp, #20]
    12f0:	b94017e1 	ldr	w1, [sp, #20]
    12f4:	b9402fe0 	ldr	w0, [sp, #44]
    12f8:	6b00003f 	cmp	w1, w0
    12fc:	540000aa 	b.ge	1310 <find_nearest_color_AIO+0x104>  // b.tcont
    1300:	b94017e0 	ldr	w0, [sp, #20]
    1304:	b9002fe0 	str	w0, [sp, #44]
    1308:	b94027e0 	ldr	w0, [sp, #36]
    130c:	b9002be0 	str	w0, [sp, #40]
    1310:	b94027e0 	ldr	w0, [sp, #36]
    1314:	11000400 	add	w0, w0, #0x1
    1318:	b90027e0 	str	w0, [sp, #36]
    131c:	b94027e0 	ldr	w0, [sp, #36]
    1320:	7100141f 	cmp	w0, #0x5
    1324:	54fff86d 	b.le	1230 <find_nearest_color_AIO+0x24>
    1328:	b9402be0 	ldr	w0, [sp, #40]
    132c:	12001c00 	and	w0, w0, #0xff
    1330:	9100c3ff 	add	sp, sp, #0x30
    1334:	d65f03c0 	ret

0000000000001338 <ImageProcess_Dither_AIO>:
    1338:	a9b17bfd 	stp	x29, x30, [sp, #-240]!
    133c:	910003fd 	mov	x29, sp
    1340:	f9000fa0 	str	x0, [x29, #24]
    1344:	b90017a1 	str	w1, [x29, #20]
    1348:	b90013a2 	str	w2, [x29, #16]
    134c:	b900efbf 	str	wzr, [x29, #236]
    1350:	b98017a1 	ldrsw	x1, [x29, #20]
    1354:	aa0103e0 	mov	x0, x1
    1358:	d37ff800 	lsl	x0, x0, #1
    135c:	8b010000 	add	x0, x0, x1
    1360:	d37ff800 	lsl	x0, x0, #1
    1364:	94000000 	bl	0 <malloc>
    1368:	f9004fa0 	str	x0, [x29, #152]
    136c:	b98017a1 	ldrsw	x1, [x29, #20]
    1370:	aa0103e0 	mov	x0, x1
    1374:	d37ff800 	lsl	x0, x0, #1
    1378:	8b010000 	add	x0, x0, x1
    137c:	d37ff800 	lsl	x0, x0, #1
    1380:	94000000 	bl	0 <malloc>
    1384:	f90053a0 	str	x0, [x29, #160]
    1388:	b98017a1 	ldrsw	x1, [x29, #20]
    138c:	aa0103e0 	mov	x0, x1
    1390:	d37ff800 	lsl	x0, x0, #1
    1394:	8b010000 	add	x0, x0, x1
    1398:	d37ff800 	lsl	x0, x0, #1
    139c:	94000000 	bl	0 <malloc>
    13a0:	f90057a0 	str	x0, [x29, #168]
    13a4:	f9404fa3 	ldr	x3, [x29, #152]
    13a8:	b98017a1 	ldrsw	x1, [x29, #20]
    13ac:	aa0103e0 	mov	x0, x1
    13b0:	d37ff800 	lsl	x0, x0, #1
    13b4:	8b010000 	add	x0, x0, x1
    13b8:	d37ff800 	lsl	x0, x0, #1
    13bc:	aa0003e2 	mov	x2, x0
    13c0:	52800001 	mov	w1, #0x0                   	// #0
    13c4:	aa0303e0 	mov	x0, x3
    13c8:	94000000 	bl	0 <memset>
    13cc:	f94053a3 	ldr	x3, [x29, #160]
    13d0:	b98017a1 	ldrsw	x1, [x29, #20]
    13d4:	aa0103e0 	mov	x0, x1
    13d8:	d37ff800 	lsl	x0, x0, #1
    13dc:	8b010000 	add	x0, x0, x1
    13e0:	d37ff800 	lsl	x0, x0, #1
    13e4:	aa0003e2 	mov	x2, x0
    13e8:	52800001 	mov	w1, #0x0                   	// #0
    13ec:	aa0303e0 	mov	x0, x3
    13f0:	94000000 	bl	0 <memset>
    13f4:	f94057a3 	ldr	x3, [x29, #168]
    13f8:	b98017a1 	ldrsw	x1, [x29, #20]
    13fc:	aa0103e0 	mov	x0, x1
    1400:	d37ff800 	lsl	x0, x0, #1
    1404:	8b010000 	add	x0, x0, x1
    1408:	d37ff800 	lsl	x0, x0, #1
    140c:	aa0003e2 	mov	x2, x0
    1410:	52800001 	mov	w1, #0x0                   	// #0
    1414:	aa0303e0 	mov	x0, x3
    1418:	94000000 	bl	0 <memset>
    141c:	f9404fa0 	ldr	x0, [x29, #152]
    1420:	f100001f 	cmp	x0, #0x0
    1424:	540000a1 	b.ne	1438 <ImageProcess_Dither_AIO+0x100>  // b.any
    1428:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    142c:	91000000 	add	x0, x0, #0x0
    1430:	94000000 	bl	0 <puts>
    1434:	14000197 	b	1a90 <ImageProcess_Dither_AIO+0x758>
    1438:	f94053a0 	ldr	x0, [x29, #160]
    143c:	f100001f 	cmp	x0, #0x0
    1440:	540000a1 	b.ne	1454 <ImageProcess_Dither_AIO+0x11c>  // b.any
    1444:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    1448:	91000000 	add	x0, x0, #0x0
    144c:	94000000 	bl	0 <puts>
    1450:	14000190 	b	1a90 <ImageProcess_Dither_AIO+0x758>
    1454:	f94057a0 	ldr	x0, [x29, #168]
    1458:	f100001f 	cmp	x0, #0x0
    145c:	540000a1 	b.ne	1470 <ImageProcess_Dither_AIO+0x138>  // b.any
    1460:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    1464:	91000000 	add	x0, x0, #0x0
    1468:	94000000 	bl	0 <puts>
    146c:	14000189 	b	1a90 <ImageProcess_Dither_AIO+0x758>
    1470:	b900ebbf 	str	wzr, [x29, #232]
    1474:	1400017d 	b	1a68 <ImageProcess_Dither_AIO+0x730>
    1478:	b900e7bf 	str	wzr, [x29, #228]
    147c:	14000154 	b	19cc <ImageProcess_Dither_AIO+0x694>
    1480:	b980efa0 	ldrsw	x0, [x29, #236]
    1484:	f9400fa1 	ldr	x1, [x29, #24]
    1488:	8b000020 	add	x0, x1, x0
    148c:	39400000 	ldrb	w0, [x0]
    1490:	2a0003e3 	mov	w3, w0
    1494:	f9404fa2 	ldr	x2, [x29, #152]
    1498:	b980e7a1 	ldrsw	x1, [x29, #228]
    149c:	aa0103e0 	mov	x0, x1
    14a0:	d37ff800 	lsl	x0, x0, #1
    14a4:	8b010000 	add	x0, x0, x1
    14a8:	d37ff800 	lsl	x0, x0, #1
    14ac:	8b000040 	add	x0, x2, x0
    14b0:	79c00000 	ldrsh	w0, [x0]
    14b4:	0b000060 	add	w0, w3, w0
    14b8:	b900dfa0 	str	w0, [x29, #220]
    14bc:	b980efa0 	ldrsw	x0, [x29, #236]
    14c0:	91000400 	add	x0, x0, #0x1
    14c4:	f9400fa1 	ldr	x1, [x29, #24]
    14c8:	8b000020 	add	x0, x1, x0
    14cc:	39400000 	ldrb	w0, [x0]
    14d0:	2a0003e3 	mov	w3, w0
    14d4:	f9404fa2 	ldr	x2, [x29, #152]
    14d8:	b980e7a1 	ldrsw	x1, [x29, #228]
    14dc:	aa0103e0 	mov	x0, x1
    14e0:	d37ff800 	lsl	x0, x0, #1
    14e4:	8b010000 	add	x0, x0, x1
    14e8:	d37ff800 	lsl	x0, x0, #1
    14ec:	8b000040 	add	x0, x2, x0
    14f0:	79c00400 	ldrsh	w0, [x0, #2]
    14f4:	0b000060 	add	w0, w3, w0
    14f8:	b900dba0 	str	w0, [x29, #216]
    14fc:	b980efa0 	ldrsw	x0, [x29, #236]
    1500:	91000800 	add	x0, x0, #0x2
    1504:	f9400fa1 	ldr	x1, [x29, #24]
    1508:	8b000020 	add	x0, x1, x0
    150c:	39400000 	ldrb	w0, [x0]
    1510:	2a0003e3 	mov	w3, w0
    1514:	f9404fa2 	ldr	x2, [x29, #152]
    1518:	b980e7a1 	ldrsw	x1, [x29, #228]
    151c:	aa0103e0 	mov	x0, x1
    1520:	d37ff800 	lsl	x0, x0, #1
    1524:	8b010000 	add	x0, x0, x1
    1528:	d37ff800 	lsl	x0, x0, #1
    152c:	8b000040 	add	x0, x2, x0
    1530:	79c00800 	ldrsh	w0, [x0, #4]
    1534:	0b000060 	add	w0, w3, w0
    1538:	b900d7a0 	str	w0, [x29, #212]
    153c:	b940dfa0 	ldr	w0, [x29, #220]
    1540:	1e220000 	scvtf	s0, w0
    1544:	0f000401 	movi	v1.2s, #0x0
    1548:	94000000 	bl	0 <fmaxf>
    154c:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1550:	1e270001 	fmov	s1, w0
    1554:	94000000 	bl	0 <fminf>
    1558:	1e380000 	fcvtzs	w0, s0
    155c:	b900dfa0 	str	w0, [x29, #220]
    1560:	b940dba0 	ldr	w0, [x29, #216]
    1564:	1e220000 	scvtf	s0, w0
    1568:	0f000401 	movi	v1.2s, #0x0
    156c:	94000000 	bl	0 <fmaxf>
    1570:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1574:	1e270001 	fmov	s1, w0
    1578:	94000000 	bl	0 <fminf>
    157c:	1e380000 	fcvtzs	w0, s0
    1580:	b900dba0 	str	w0, [x29, #216]
    1584:	b940d7a0 	ldr	w0, [x29, #212]
    1588:	1e220000 	scvtf	s0, w0
    158c:	0f000401 	movi	v1.2s, #0x0
    1590:	94000000 	bl	0 <fmaxf>
    1594:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1598:	1e270001 	fmov	s1, w0
    159c:	94000000 	bl	0 <fminf>
    15a0:	1e380000 	fcvtzs	w0, s0
    15a4:	b900d7a0 	str	w0, [x29, #212]
    15a8:	b940d7a2 	ldr	w2, [x29, #212]
    15ac:	b940dba1 	ldr	w1, [x29, #216]
    15b0:	b940dfa0 	ldr	w0, [x29, #220]
    15b4:	97ffff16 	bl	120c <find_nearest_color_AIO>
    15b8:	39034fa0 	strb	w0, [x29, #211]
    15bc:	39434fa1 	ldrb	w1, [x29, #211]
    15c0:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    15c4:	91000002 	add	x2, x0, #0x0
    15c8:	93407c21 	sxtw	x1, w1
    15cc:	aa0103e0 	mov	x0, x1
    15d0:	d37ff800 	lsl	x0, x0, #1
    15d4:	8b010000 	add	x0, x0, x1
    15d8:	8b000040 	add	x0, x2, x0
    15dc:	39400000 	ldrb	w0, [x0]
    15e0:	39034ba0 	strb	w0, [x29, #210]
    15e4:	39434fa1 	ldrb	w1, [x29, #211]
    15e8:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    15ec:	91000002 	add	x2, x0, #0x0
    15f0:	93407c21 	sxtw	x1, w1
    15f4:	aa0103e0 	mov	x0, x1
    15f8:	d37ff800 	lsl	x0, x0, #1
    15fc:	8b010000 	add	x0, x0, x1
    1600:	8b000040 	add	x0, x2, x0
    1604:	39400400 	ldrb	w0, [x0, #1]
    1608:	390347a0 	strb	w0, [x29, #209]
    160c:	39434fa1 	ldrb	w1, [x29, #211]
    1610:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    1614:	91000002 	add	x2, x0, #0x0
    1618:	93407c21 	sxtw	x1, w1
    161c:	aa0103e0 	mov	x0, x1
    1620:	d37ff800 	lsl	x0, x0, #1
    1624:	8b010000 	add	x0, x0, x1
    1628:	8b000040 	add	x0, x2, x0
    162c:	39400800 	ldrb	w0, [x0, #2]
    1630:	390343a0 	strb	w0, [x29, #208]
    1634:	b980efa0 	ldrsw	x0, [x29, #236]
    1638:	f9400fa1 	ldr	x1, [x29, #24]
    163c:	8b000020 	add	x0, x1, x0
    1640:	39434ba1 	ldrb	w1, [x29, #210]
    1644:	39000001 	strb	w1, [x0]
    1648:	b980efa0 	ldrsw	x0, [x29, #236]
    164c:	91000400 	add	x0, x0, #0x1
    1650:	f9400fa1 	ldr	x1, [x29, #24]
    1654:	8b000020 	add	x0, x1, x0
    1658:	394347a1 	ldrb	w1, [x29, #209]
    165c:	39000001 	strb	w1, [x0]
    1660:	b980efa0 	ldrsw	x0, [x29, #236]
    1664:	91000800 	add	x0, x0, #0x2
    1668:	f9400fa1 	ldr	x1, [x29, #24]
    166c:	8b000020 	add	x0, x1, x0
    1670:	394343a1 	ldrb	w1, [x29, #208]
    1674:	39000001 	strb	w1, [x0]
    1678:	b940efa0 	ldr	w0, [x29, #236]
    167c:	11000c00 	add	w0, w0, #0x3
    1680:	b900efa0 	str	w0, [x29, #236]
    1684:	39434ba0 	ldrb	w0, [x29, #210]
    1688:	b940dfa1 	ldr	w1, [x29, #220]
    168c:	4b000020 	sub	w0, w1, w0
    1690:	b900cfa0 	str	w0, [x29, #204]
    1694:	394347a0 	ldrb	w0, [x29, #209]
    1698:	b940dba1 	ldr	w1, [x29, #216]
    169c:	4b000020 	sub	w0, w1, w0
    16a0:	b900cba0 	str	w0, [x29, #200]
    16a4:	394343a0 	ldrb	w0, [x29, #208]
    16a8:	b940d7a1 	ldr	w1, [x29, #212]
    16ac:	4b000020 	sub	w0, w1, w0
    16b0:	b900c7a0 	str	w0, [x29, #196]
    16b4:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    16b8:	91000000 	add	x0, x0, #0x0
    16bc:	910203a2 	add	x2, x29, #0x80
    16c0:	aa0003e3 	mov	x3, x0
    16c4:	a9400460 	ldp	x0, x1, [x3]
    16c8:	a9000440 	stp	x0, x1, [x2]
    16cc:	f9400860 	ldr	x0, [x3, #16]
    16d0:	f9000840 	str	x0, [x2, #16]
    16d4:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    16d8:	91000001 	add	x1, x0, #0x0
    16dc:	910083a0 	add	x0, x29, #0x20
    16e0:	a9400c22 	ldp	x2, x3, [x1]
    16e4:	a9000c02 	stp	x2, x3, [x0]
    16e8:	a9410c22 	ldp	x2, x3, [x1, #16]
    16ec:	a9010c02 	stp	x2, x3, [x0, #16]
    16f0:	a9420c22 	ldp	x2, x3, [x1, #32]
    16f4:	a9020c02 	stp	x2, x3, [x0, #32]
    16f8:	a9430c22 	ldp	x2, x3, [x1, #48]
    16fc:	a9030c02 	stp	x2, x3, [x0, #48]
    1700:	a9440c22 	ldp	x2, x3, [x1, #64]
    1704:	a9040c02 	stp	x2, x3, [x0, #64]
    1708:	a9450821 	ldp	x1, x2, [x1, #80]
    170c:	a9050801 	stp	x1, x2, [x0, #80]
    1710:	b900e3bf 	str	wzr, [x29, #224]
    1714:	140000a8 	b	19b4 <ImageProcess_Dither_AIO+0x67c>
    1718:	b940e3a0 	ldr	w0, [x29, #224]
    171c:	531f7800 	lsl	w0, w0, #1
    1720:	93407c00 	sxtw	x0, w0
    1724:	d37ef400 	lsl	x0, x0, #2
    1728:	910083a1 	add	x1, x29, #0x20
    172c:	b8606820 	ldr	w0, [x1, x0]
    1730:	b900c3a0 	str	w0, [x29, #192]
    1734:	b940e3a0 	ldr	w0, [x29, #224]
    1738:	531f7800 	lsl	w0, w0, #1
    173c:	11000400 	add	w0, w0, #0x1
    1740:	93407c00 	sxtw	x0, w0
    1744:	d37ef400 	lsl	x0, x0, #2
    1748:	910083a1 	add	x1, x29, #0x20
    174c:	b8606820 	ldr	w0, [x1, x0]
    1750:	b900bfa0 	str	w0, [x29, #188]
    1754:	b980e3a0 	ldrsw	x0, [x29, #224]
    1758:	d37ff800 	lsl	x0, x0, #1
    175c:	910203a1 	add	x1, x29, #0x80
    1760:	78e06820 	ldrsh	w0, [x1, x0]
    1764:	b900bba0 	str	w0, [x29, #184]
    1768:	b940e7a1 	ldr	w1, [x29, #228]
    176c:	b940c3a0 	ldr	w0, [x29, #192]
    1770:	0b000020 	add	w0, w1, w0
    1774:	7100001f 	cmp	w0, #0x0
    1778:	5400112b 	b.lt	199c <ImageProcess_Dither_AIO+0x664>  // b.tstop
    177c:	b940e7a1 	ldr	w1, [x29, #228]
    1780:	b940c3a0 	ldr	w0, [x29, #192]
    1784:	0b000020 	add	w0, w1, w0
    1788:	b94017a1 	ldr	w1, [x29, #20]
    178c:	6b00003f 	cmp	w1, w0
    1790:	5400106d 	b.le	199c <ImageProcess_Dither_AIO+0x664>
    1794:	b940eba1 	ldr	w1, [x29, #232]
    1798:	b940bfa0 	ldr	w0, [x29, #188]
    179c:	0b000020 	add	w0, w1, w0
    17a0:	b94013a1 	ldr	w1, [x29, #16]
    17a4:	6b00003f 	cmp	w1, w0
    17a8:	54000fed 	b.le	19a4 <ImageProcess_Dither_AIO+0x66c>
    17ac:	b940bfa0 	ldr	w0, [x29, #188]
    17b0:	7100001f 	cmp	w0, #0x0
    17b4:	54000100 	b.eq	17d4 <ImageProcess_Dither_AIO+0x49c>  // b.none
    17b8:	b940bfa0 	ldr	w0, [x29, #188]
    17bc:	7100041f 	cmp	w0, #0x1
    17c0:	54000061 	b.ne	17cc <ImageProcess_Dither_AIO+0x494>  // b.any
    17c4:	52800020 	mov	w0, #0x1                   	// #1
    17c8:	14000004 	b	17d8 <ImageProcess_Dither_AIO+0x4a0>
    17cc:	52800040 	mov	w0, #0x2                   	// #2
    17d0:	14000002 	b	17d8 <ImageProcess_Dither_AIO+0x4a0>
    17d4:	52800000 	mov	w0, #0x0                   	// #0
    17d8:	b900b7a0 	str	w0, [x29, #180]
    17dc:	b980b7a0 	ldrsw	x0, [x29, #180]
    17e0:	d37df000 	lsl	x0, x0, #3
    17e4:	910263a1 	add	x1, x29, #0x98
    17e8:	f8606822 	ldr	x2, [x1, x0]
    17ec:	b940e7a1 	ldr	w1, [x29, #228]
    17f0:	b940c3a0 	ldr	w0, [x29, #192]
    17f4:	0b000020 	add	w0, w1, w0
    17f8:	93407c01 	sxtw	x1, w0
    17fc:	aa0103e0 	mov	x0, x1
    1800:	d37ff800 	lsl	x0, x0, #1
    1804:	8b010000 	add	x0, x0, x1
    1808:	d37ff800 	lsl	x0, x0, #1
    180c:	8b000040 	add	x0, x2, x0
    1810:	79c00000 	ldrsh	w0, [x0]
    1814:	12003c01 	and	w1, w0, #0xffff
    1818:	b940cfa2 	ldr	w2, [x29, #204]
    181c:	b940bba0 	ldr	w0, [x29, #184]
    1820:	1b007c40 	mul	w0, w2, w0
    1824:	13067c00 	asr	w0, w0, #6
    1828:	12003c00 	and	w0, w0, #0xffff
    182c:	0b000020 	add	w0, w1, w0
    1830:	12003c03 	and	w3, w0, #0xffff
    1834:	b980b7a0 	ldrsw	x0, [x29, #180]
    1838:	d37df000 	lsl	x0, x0, #3
    183c:	910263a1 	add	x1, x29, #0x98
    1840:	f8606822 	ldr	x2, [x1, x0]
    1844:	b940e7a1 	ldr	w1, [x29, #228]
    1848:	b940c3a0 	ldr	w0, [x29, #192]
    184c:	0b000020 	add	w0, w1, w0
    1850:	93407c01 	sxtw	x1, w0
    1854:	aa0103e0 	mov	x0, x1
    1858:	d37ff800 	lsl	x0, x0, #1
    185c:	8b010000 	add	x0, x0, x1
    1860:	d37ff800 	lsl	x0, x0, #1
    1864:	8b000040 	add	x0, x2, x0
    1868:	13003c61 	sxth	w1, w3
    186c:	79000001 	strh	w1, [x0]
    1870:	b980b7a0 	ldrsw	x0, [x29, #180]
    1874:	d37df000 	lsl	x0, x0, #3
    1878:	910263a1 	add	x1, x29, #0x98
    187c:	f8606822 	ldr	x2, [x1, x0]
    1880:	b940e7a1 	ldr	w1, [x29, #228]
    1884:	b940c3a0 	ldr	w0, [x29, #192]
    1888:	0b000020 	add	w0, w1, w0
    188c:	93407c01 	sxtw	x1, w0
    1890:	aa0103e0 	mov	x0, x1
    1894:	d37ff800 	lsl	x0, x0, #1
    1898:	8b010000 	add	x0, x0, x1
    189c:	d37ff800 	lsl	x0, x0, #1
    18a0:	8b000040 	add	x0, x2, x0
    18a4:	79c00400 	ldrsh	w0, [x0, #2]
    18a8:	12003c01 	and	w1, w0, #0xffff
    18ac:	b940cba2 	ldr	w2, [x29, #200]
    18b0:	b940bba0 	ldr	w0, [x29, #184]
    18b4:	1b007c40 	mul	w0, w2, w0
    18b8:	13067c00 	asr	w0, w0, #6
    18bc:	12003c00 	and	w0, w0, #0xffff
    18c0:	0b000020 	add	w0, w1, w0
    18c4:	12003c03 	and	w3, w0, #0xffff
    18c8:	b980b7a0 	ldrsw	x0, [x29, #180]
    18cc:	d37df000 	lsl	x0, x0, #3
    18d0:	910263a1 	add	x1, x29, #0x98
    18d4:	f8606822 	ldr	x2, [x1, x0]
    18d8:	b940e7a1 	ldr	w1, [x29, #228]
    18dc:	b940c3a0 	ldr	w0, [x29, #192]
    18e0:	0b000020 	add	w0, w1, w0
    18e4:	93407c01 	sxtw	x1, w0
    18e8:	aa0103e0 	mov	x0, x1
    18ec:	d37ff800 	lsl	x0, x0, #1
    18f0:	8b010000 	add	x0, x0, x1
    18f4:	d37ff800 	lsl	x0, x0, #1
    18f8:	8b000040 	add	x0, x2, x0
    18fc:	13003c61 	sxth	w1, w3
    1900:	79000401 	strh	w1, [x0, #2]
    1904:	b980b7a0 	ldrsw	x0, [x29, #180]
    1908:	d37df000 	lsl	x0, x0, #3
    190c:	910263a1 	add	x1, x29, #0x98
    1910:	f8606822 	ldr	x2, [x1, x0]
    1914:	b940e7a1 	ldr	w1, [x29, #228]
    1918:	b940c3a0 	ldr	w0, [x29, #192]
    191c:	0b000020 	add	w0, w1, w0
    1920:	93407c01 	sxtw	x1, w0
    1924:	aa0103e0 	mov	x0, x1
    1928:	d37ff800 	lsl	x0, x0, #1
    192c:	8b010000 	add	x0, x0, x1
    1930:	d37ff800 	lsl	x0, x0, #1
    1934:	8b000040 	add	x0, x2, x0
    1938:	79c00800 	ldrsh	w0, [x0, #4]
    193c:	12003c01 	and	w1, w0, #0xffff
    1940:	b940c7a2 	ldr	w2, [x29, #196]
    1944:	b940bba0 	ldr	w0, [x29, #184]
    1948:	1b007c40 	mul	w0, w2, w0
    194c:	13067c00 	asr	w0, w0, #6
    1950:	12003c00 	and	w0, w0, #0xffff
    1954:	0b000020 	add	w0, w1, w0
    1958:	12003c03 	and	w3, w0, #0xffff
    195c:	b980b7a0 	ldrsw	x0, [x29, #180]
    1960:	d37df000 	lsl	x0, x0, #3
    1964:	910263a1 	add	x1, x29, #0x98
    1968:	f8606822 	ldr	x2, [x1, x0]
    196c:	b940e7a1 	ldr	w1, [x29, #228]
    1970:	b940c3a0 	ldr	w0, [x29, #192]
    1974:	0b000020 	add	w0, w1, w0
    1978:	93407c01 	sxtw	x1, w0
    197c:	aa0103e0 	mov	x0, x1
    1980:	d37ff800 	lsl	x0, x0, #1
    1984:	8b010000 	add	x0, x0, x1
    1988:	d37ff800 	lsl	x0, x0, #1
    198c:	8b000040 	add	x0, x2, x0
    1990:	13003c61 	sxth	w1, w3
    1994:	79000801 	strh	w1, [x0, #4]
    1998:	14000004 	b	19a8 <ImageProcess_Dither_AIO+0x670>
    199c:	d503201f 	nop
    19a0:	14000002 	b	19a8 <ImageProcess_Dither_AIO+0x670>
    19a4:	d503201f 	nop
    19a8:	b940e3a0 	ldr	w0, [x29, #224]
    19ac:	11000400 	add	w0, w0, #0x1
    19b0:	b900e3a0 	str	w0, [x29, #224]
    19b4:	b940e3a0 	ldr	w0, [x29, #224]
    19b8:	71002c1f 	cmp	w0, #0xb
    19bc:	54ffeaed 	b.le	1718 <ImageProcess_Dither_AIO+0x3e0>
    19c0:	b940e7a0 	ldr	w0, [x29, #228]
    19c4:	11000400 	add	w0, w0, #0x1
    19c8:	b900e7a0 	str	w0, [x29, #228]
    19cc:	b940e7a1 	ldr	w1, [x29, #228]
    19d0:	b94017a0 	ldr	w0, [x29, #20]
    19d4:	6b00003f 	cmp	w1, w0
    19d8:	54ffd54b 	b.lt	1480 <ImageProcess_Dither_AIO+0x148>  // b.tstop
    19dc:	f9404fa3 	ldr	x3, [x29, #152]
    19e0:	f94053a4 	ldr	x4, [x29, #160]
    19e4:	b98017a1 	ldrsw	x1, [x29, #20]
    19e8:	aa0103e0 	mov	x0, x1
    19ec:	d37ff800 	lsl	x0, x0, #1
    19f0:	8b010000 	add	x0, x0, x1
    19f4:	d37ff800 	lsl	x0, x0, #1
    19f8:	aa0003e2 	mov	x2, x0
    19fc:	aa0403e1 	mov	x1, x4
    1a00:	aa0303e0 	mov	x0, x3
    1a04:	94000000 	bl	0 <memcpy>
    1a08:	f94053a3 	ldr	x3, [x29, #160]
    1a0c:	f94057a4 	ldr	x4, [x29, #168]
    1a10:	b98017a1 	ldrsw	x1, [x29, #20]
    1a14:	aa0103e0 	mov	x0, x1
    1a18:	d37ff800 	lsl	x0, x0, #1
    1a1c:	8b010000 	add	x0, x0, x1
    1a20:	d37ff800 	lsl	x0, x0, #1
    1a24:	aa0003e2 	mov	x2, x0
    1a28:	aa0403e1 	mov	x1, x4
    1a2c:	aa0303e0 	mov	x0, x3
    1a30:	94000000 	bl	0 <memcpy>
    1a34:	f94057a3 	ldr	x3, [x29, #168]
    1a38:	b98017a1 	ldrsw	x1, [x29, #20]
    1a3c:	aa0103e0 	mov	x0, x1
    1a40:	d37ff800 	lsl	x0, x0, #1
    1a44:	8b010000 	add	x0, x0, x1
    1a48:	d37ff800 	lsl	x0, x0, #1
    1a4c:	aa0003e2 	mov	x2, x0
    1a50:	52800001 	mov	w1, #0x0                   	// #0
    1a54:	aa0303e0 	mov	x0, x3
    1a58:	94000000 	bl	0 <memset>
    1a5c:	b940eba0 	ldr	w0, [x29, #232]
    1a60:	11000400 	add	w0, w0, #0x1
    1a64:	b900eba0 	str	w0, [x29, #232]
    1a68:	b940eba1 	ldr	w1, [x29, #232]
    1a6c:	b94013a0 	ldr	w0, [x29, #16]
    1a70:	6b00003f 	cmp	w1, w0
    1a74:	54ffd02b 	b.lt	1478 <ImageProcess_Dither_AIO+0x140>  // b.tstop
    1a78:	f9404fa0 	ldr	x0, [x29, #152]
    1a7c:	94000000 	bl	0 <free>
    1a80:	f94053a0 	ldr	x0, [x29, #160]
    1a84:	94000000 	bl	0 <free>
    1a88:	f94057a0 	ldr	x0, [x29, #168]
    1a8c:	94000000 	bl	0 <free>
    1a90:	a8cf7bfd 	ldp	x29, x30, [sp], #240
    1a94:	d65f03c0 	ret

0000000000001a98 <ImageProcess_Spectra6_AIO>:
    1a98:	a9bd7bfd 	stp	x29, x30, [sp, #-48]!
    1a9c:	910003fd 	mov	x29, sp
    1aa0:	bd002fa0 	str	s0, [x29, #44]
    1aa4:	bd002ba1 	str	s1, [x29, #40]
    1aa8:	bd0027a2 	str	s2, [x29, #36]
    1aac:	bd0023a3 	str	s3, [x29, #32]
    1ab0:	f9000fa0 	str	x0, [x29, #24]
    1ab4:	b90017a1 	str	w1, [x29, #20]
    1ab8:	b90013a2 	str	w2, [x29, #16]
    1abc:	b94013a2 	ldr	w2, [x29, #16]
    1ac0:	b94017a1 	ldr	w1, [x29, #20]
    1ac4:	f9400fa0 	ldr	x0, [x29, #24]
    1ac8:	bd4023a3 	ldr	s3, [x29, #32]
    1acc:	bd4027a2 	ldr	s2, [x29, #36]
    1ad0:	bd402ba1 	ldr	s1, [x29, #40]
    1ad4:	bd402fa0 	ldr	s0, [x29, #44]
    1ad8:	94000000 	bl	1180 <ImageProcess_ColorEnhace>
    1adc:	b94013a2 	ldr	w2, [x29, #16]
    1ae0:	b94017a1 	ldr	w1, [x29, #20]
    1ae4:	f9400fa0 	ldr	x0, [x29, #24]
    1ae8:	97fffe14 	bl	1338 <ImageProcess_Dither_AIO>
    1aec:	d503201f 	nop
    1af0:	a8c37bfd 	ldp	x29, x30, [sp], #48
    1af4:	d65f03c0 	ret

0000000000001af8 <IndexMapping_Spectra6_AIO_Y4>:
    1af8:	a9ba7bfd 	stp	x29, x30, [sp, #-96]!
    1afc:	910003fd 	mov	x29, sp
    1b00:	f90017a0 	str	x0, [x29, #40]
    1b04:	f90013a1 	str	x1, [x29, #32]
    1b08:	b9001fa2 	str	w2, [x29, #28]
    1b0c:	b9001ba3 	str	w3, [x29, #24]
    1b10:	1e2f1000 	fmov	s0, #1.500000000000000000e+00
    1b14:	bd0053a0 	str	s0, [x29, #80]
    1b18:	529999a0 	mov	w0, #0xcccd                	// #52429
    1b1c:	72a7a980 	movk	w0, #0x3d4c, lsl #16
    1b20:	1e270000 	fmov	s0, w0
    1b24:	bd004fa0 	str	s0, [x29, #76]
    1b28:	1e249000 	fmov	s0, #1.000000000000000000e+01
    1b2c:	bd004ba0 	str	s0, [x29, #72]
    1b30:	b90047bf 	str	wzr, [x29, #68]
    1b34:	b9401ba2 	ldr	w2, [x29, #24]
    1b38:	b9401fa1 	ldr	w1, [x29, #28]
    1b3c:	f94017a0 	ldr	x0, [x29, #40]
    1b40:	bd4047a3 	ldr	s3, [x29, #68]
    1b44:	bd404ba2 	ldr	s2, [x29, #72]
    1b48:	bd404fa1 	ldr	s1, [x29, #76]
    1b4c:	bd4053a0 	ldr	s0, [x29, #80]
    1b50:	97ffffd2 	bl	1a98 <ImageProcess_Spectra6_AIO>
    1b54:	b9005fbf 	str	wzr, [x29, #92]
    1b58:	b9005bbf 	str	wzr, [x29, #88]
    1b5c:	1400006a 	b	1d04 <IndexMapping_Spectra6_AIO_Y4+0x20c>
    1b60:	b9805fa0 	ldrsw	x0, [x29, #92]
    1b64:	f94017a1 	ldr	x1, [x29, #40]
    1b68:	8b000020 	add	x0, x1, x0
    1b6c:	39400000 	ldrb	w0, [x0]
    1b70:	39010fa0 	strb	w0, [x29, #67]
    1b74:	b9805fa0 	ldrsw	x0, [x29, #92]
    1b78:	91000400 	add	x0, x0, #0x1
    1b7c:	f94017a1 	ldr	x1, [x29, #40]
    1b80:	8b000020 	add	x0, x1, x0
    1b84:	39400000 	ldrb	w0, [x0]
    1b88:	39010ba0 	strb	w0, [x29, #66]
    1b8c:	b9805fa0 	ldrsw	x0, [x29, #92]
    1b90:	91000800 	add	x0, x0, #0x2
    1b94:	f94017a1 	ldr	x1, [x29, #40]
    1b98:	8b000020 	add	x0, x1, x0
    1b9c:	39400000 	ldrb	w0, [x0]
    1ba0:	390107a0 	strb	w0, [x29, #65]
    1ba4:	b90057bf 	str	wzr, [x29, #84]
    1ba8:	1400004e 	b	1ce0 <IndexMapping_Spectra6_AIO_Y4+0x1e8>
    1bac:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    1bb0:	91000002 	add	x2, x0, #0x0
    1bb4:	b98057a1 	ldrsw	x1, [x29, #84]
    1bb8:	aa0103e0 	mov	x0, x1
    1bbc:	d37ff800 	lsl	x0, x0, #1
    1bc0:	8b010000 	add	x0, x0, x1
    1bc4:	8b000040 	add	x0, x2, x0
    1bc8:	39400000 	ldrb	w0, [x0]
    1bcc:	39410fa1 	ldrb	w1, [x29, #67]
    1bd0:	6b00003f 	cmp	w1, w0
    1bd4:	54000801 	b.ne	1cd4 <IndexMapping_Spectra6_AIO_Y4+0x1dc>  // b.any
    1bd8:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    1bdc:	91000002 	add	x2, x0, #0x0
    1be0:	b98057a1 	ldrsw	x1, [x29, #84]
    1be4:	aa0103e0 	mov	x0, x1
    1be8:	d37ff800 	lsl	x0, x0, #1
    1bec:	8b010000 	add	x0, x0, x1
    1bf0:	8b000040 	add	x0, x2, x0
    1bf4:	39400400 	ldrb	w0, [x0, #1]
    1bf8:	39410ba1 	ldrb	w1, [x29, #66]
    1bfc:	6b00003f 	cmp	w1, w0
    1c00:	540006a1 	b.ne	1cd4 <IndexMapping_Spectra6_AIO_Y4+0x1dc>  // b.any
    1c04:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    1c08:	91000002 	add	x2, x0, #0x0
    1c0c:	b98057a1 	ldrsw	x1, [x29, #84]
    1c10:	aa0103e0 	mov	x0, x1
    1c14:	d37ff800 	lsl	x0, x0, #1
    1c18:	8b010000 	add	x0, x0, x1
    1c1c:	8b000040 	add	x0, x2, x0
    1c20:	39400800 	ldrb	w0, [x0, #2]
    1c24:	394107a1 	ldrb	w1, [x29, #65]
    1c28:	6b00003f 	cmp	w1, w0
    1c2c:	54000541 	b.ne	1cd4 <IndexMapping_Spectra6_AIO_Y4+0x1dc>  // b.any
    1c30:	90000000 	adrp	x0, 18 <SWAP_RGB_BGR+0x18>
    1c34:	91000001 	add	x1, x0, #0x0
    1c38:	b98057a0 	ldrsw	x0, [x29, #84]
    1c3c:	38606820 	ldrb	w0, [x1, x0]
    1c40:	b9003fa0 	str	w0, [x29, #60]
    1c44:	b9405ba0 	ldr	w0, [x29, #88]
    1c48:	12000000 	and	w0, w0, #0x1
    1c4c:	7100001f 	cmp	w0, #0x0
    1c50:	540001a1 	b.ne	1c84 <IndexMapping_Spectra6_AIO_Y4+0x18c>  // b.any
    1c54:	b9403fa0 	ldr	w0, [x29, #60]
    1c58:	531c6c02 	lsl	w2, w0, #4
    1c5c:	b9405ba0 	ldr	w0, [x29, #88]
    1c60:	531f7c01 	lsr	w1, w0, #31
    1c64:	0b000020 	add	w0, w1, w0
    1c68:	13017c00 	asr	w0, w0, #1
    1c6c:	93407c00 	sxtw	x0, w0
    1c70:	f94013a1 	ldr	x1, [x29, #32]
    1c74:	8b000020 	add	x0, x1, x0
    1c78:	12001c41 	and	w1, w2, #0xff
    1c7c:	39000001 	strb	w1, [x0]
    1c80:	1400001b 	b	1cec <IndexMapping_Spectra6_AIO_Y4+0x1f4>
    1c84:	b9405ba0 	ldr	w0, [x29, #88]
    1c88:	531f7c01 	lsr	w1, w0, #31
    1c8c:	0b000020 	add	w0, w1, w0
    1c90:	13017c00 	asr	w0, w0, #1
    1c94:	2a0003e3 	mov	w3, w0
    1c98:	93407c60 	sxtw	x0, w3
    1c9c:	f94013a1 	ldr	x1, [x29, #32]
    1ca0:	8b000020 	add	x0, x1, x0
    1ca4:	39400002 	ldrb	w2, [x0]
    1ca8:	b9403fa0 	ldr	w0, [x29, #60]
    1cac:	12001c00 	and	w0, w0, #0xff
    1cb0:	12000c00 	and	w0, w0, #0xf
    1cb4:	12001c01 	and	w1, w0, #0xff
    1cb8:	93407c60 	sxtw	x0, w3
    1cbc:	f94013a3 	ldr	x3, [x29, #32]
    1cc0:	8b000060 	add	x0, x3, x0
    1cc4:	0b010041 	add	w1, w2, w1
    1cc8:	12001c21 	and	w1, w1, #0xff
    1ccc:	39000001 	strb	w1, [x0]
    1cd0:	14000007 	b	1cec <IndexMapping_Spectra6_AIO_Y4+0x1f4>
    1cd4:	b94057a0 	ldr	w0, [x29, #84]
    1cd8:	11000400 	add	w0, w0, #0x1
    1cdc:	b90057a0 	str	w0, [x29, #84]
    1ce0:	b94057a0 	ldr	w0, [x29, #84]
    1ce4:	7100141f 	cmp	w0, #0x5
    1ce8:	54fff62d 	b.le	1bac <IndexMapping_Spectra6_AIO_Y4+0xb4>
    1cec:	b9405fa0 	ldr	w0, [x29, #92]
    1cf0:	11000c00 	add	w0, w0, #0x3
    1cf4:	b9005fa0 	str	w0, [x29, #92]
    1cf8:	b9405ba0 	ldr	w0, [x29, #88]
    1cfc:	11000400 	add	w0, w0, #0x1
    1d00:	b9005ba0 	str	w0, [x29, #88]
    1d04:	b9401fa1 	ldr	w1, [x29, #28]
    1d08:	b9401ba0 	ldr	w0, [x29, #24]
    1d0c:	1b007c20 	mul	w0, w1, w0
    1d10:	b9405ba1 	ldr	w1, [x29, #88]
    1d14:	6b00003f 	cmp	w1, w0
    1d18:	54fff24b 	b.lt	1b60 <IndexMapping_Spectra6_AIO_Y4+0x68>  // b.tstop
    1d1c:	d503201f 	nop
    1d20:	a8c67bfd 	ldp	x29, x30, [sp], #96
    1d24:	d65f03c0 	ret

0000000000001d28 <IndexMapping_Spectra6_AIO_Y8>:
    1d28:	a9bb7bfd 	stp	x29, x30, [sp, #-80]!
    1d2c:	910003fd 	mov	x29, sp
    1d30:	f90017a0 	str	x0, [x29, #40]
    1d34:	f90013a1 	str	x1, [x29, #32]
    1d38:	b9001fa2 	str	w2, [x29, #28]
    1d3c:	b9001ba3 	str	w3, [x29, #24]
    1d40:	1e2f1000 	fmov	s0, #1.500000000000000000e+00
    1d44:	bd0043a0 	str	s0, [x29, #64]
    1d48:	b9003fbf 	str	wzr, [x29, #60]
    1d4c:	b9003bbf 	str	wzr, [x29, #56]
    1d50:	b90037bf 	str	wzr, [x29, #52]
    1d54:	b9401ba2 	ldr	w2, [x29, #24]
    1d58:	b9401fa1 	ldr	w1, [x29, #28]
    1d5c:	f94017a0 	ldr	x0, [x29, #40]
    1d60:	bd4037a3 	ldr	s3, [x29, #52]
    1d64:	bd403ba2 	ldr	s2, [x29, #56]
    1d68:	bd403fa1 	ldr	s1, [x29, #60]
    1d6c:	bd4043a0 	ldr	s0, [x29, #64]
    1d70:	97ffff4a 	bl	1a98 <ImageProcess_Spectra6_AIO>
    1d74:	b9004fbf 	str	wzr, [x29, #76]
    1d78:	b9004bbf 	str	wzr, [x29, #72]
    1d7c:	14000048 	b	1e9c <IndexMapping_Spectra6_AIO_Y8+0x174>
    1d80:	b9804fa0 	ldrsw	x0, [x29, #76]
    1d84:	f94017a1 	ldr	x1, [x29, #40]
    1d88:	8b000020 	add	x0, x1, x0
    1d8c:	39400000 	ldrb	w0, [x0]
    1d90:	3900cfa0 	strb	w0, [x29, #51]
    1d94:	b9804fa0 	ldrsw	x0, [x29, #76]
    1d98:	91000400 	add	x0, x0, #0x1
    1d9c:	f94017a1 	ldr	x1, [x29, #40]
    1da0:	8b000020 	add	x0, x1, x0
    1da4:	39400000 	ldrb	w0, [x0]
    1da8:	3900cba0 	strb	w0, [x29, #50]
    1dac:	b9804fa0 	ldrsw	x0, [x29, #76]
    1db0:	91000800 	add	x0, x0, #0x2
    1db4:	f94017a1 	ldr	x1, [x29, #40]
    1db8:	8b000020 	add	x0, x1, x0
    1dbc:	39400000 	ldrb	w0, [x0]
    1dc0:	3900c7a0 	strb	w0, [x29, #49]
    1dc4:	b90047bf 	str	wzr, [x29, #68]
    1dc8:	1400002c 	b	1e78 <IndexMapping_Spectra6_AIO_Y8+0x150>
    1dcc:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    1dd0:	91000002 	add	x2, x0, #0x0
    1dd4:	b98047a1 	ldrsw	x1, [x29, #68]
    1dd8:	aa0103e0 	mov	x0, x1
    1ddc:	d37ff800 	lsl	x0, x0, #1
    1de0:	8b010000 	add	x0, x0, x1
    1de4:	8b000040 	add	x0, x2, x0
    1de8:	39400000 	ldrb	w0, [x0]
    1dec:	3940cfa1 	ldrb	w1, [x29, #51]
    1df0:	6b00003f 	cmp	w1, w0
    1df4:	540003c1 	b.ne	1e6c <IndexMapping_Spectra6_AIO_Y8+0x144>  // b.any
    1df8:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    1dfc:	91000002 	add	x2, x0, #0x0
    1e00:	b98047a1 	ldrsw	x1, [x29, #68]
    1e04:	aa0103e0 	mov	x0, x1
    1e08:	d37ff800 	lsl	x0, x0, #1
    1e0c:	8b010000 	add	x0, x0, x1
    1e10:	8b000040 	add	x0, x2, x0
    1e14:	39400400 	ldrb	w0, [x0, #1]
    1e18:	3940cba1 	ldrb	w1, [x29, #50]
    1e1c:	6b00003f 	cmp	w1, w0
    1e20:	54000261 	b.ne	1e6c <IndexMapping_Spectra6_AIO_Y8+0x144>  // b.any
    1e24:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    1e28:	91000002 	add	x2, x0, #0x0
    1e2c:	b98047a1 	ldrsw	x1, [x29, #68]
    1e30:	aa0103e0 	mov	x0, x1
    1e34:	d37ff800 	lsl	x0, x0, #1
    1e38:	8b010000 	add	x0, x0, x1
    1e3c:	8b000040 	add	x0, x2, x0
    1e40:	39400800 	ldrb	w0, [x0, #2]
    1e44:	3940c7a1 	ldrb	w1, [x29, #49]
    1e48:	6b00003f 	cmp	w1, w0
    1e4c:	54000101 	b.ne	1e6c <IndexMapping_Spectra6_AIO_Y8+0x144>  // b.any
    1e50:	b9804ba0 	ldrsw	x0, [x29, #72]
    1e54:	f94013a1 	ldr	x1, [x29, #32]
    1e58:	8b000020 	add	x0, x1, x0
    1e5c:	b94047a1 	ldr	w1, [x29, #68]
    1e60:	12001c21 	and	w1, w1, #0xff
    1e64:	39000001 	strb	w1, [x0]
    1e68:	14000007 	b	1e84 <IndexMapping_Spectra6_AIO_Y8+0x15c>
    1e6c:	b94047a0 	ldr	w0, [x29, #68]
    1e70:	11000400 	add	w0, w0, #0x1
    1e74:	b90047a0 	str	w0, [x29, #68]
    1e78:	b94047a0 	ldr	w0, [x29, #68]
    1e7c:	7100141f 	cmp	w0, #0x5
    1e80:	54fffa6d 	b.le	1dcc <IndexMapping_Spectra6_AIO_Y8+0xa4>
    1e84:	b9404fa0 	ldr	w0, [x29, #76]
    1e88:	11000c00 	add	w0, w0, #0x3
    1e8c:	b9004fa0 	str	w0, [x29, #76]
    1e90:	b9404ba0 	ldr	w0, [x29, #72]
    1e94:	11000400 	add	w0, w0, #0x1
    1e98:	b9004ba0 	str	w0, [x29, #72]
    1e9c:	b9401fa1 	ldr	w1, [x29, #28]
    1ea0:	b9401ba0 	ldr	w0, [x29, #24]
    1ea4:	1b007c20 	mul	w0, w1, w0
    1ea8:	b9404ba1 	ldr	w1, [x29, #72]
    1eac:	6b00003f 	cmp	w1, w0
    1eb0:	54fff68b 	b.lt	1d80 <IndexMapping_Spectra6_AIO_Y8+0x58>  // b.tstop
    1eb4:	d503201f 	nop
    1eb8:	a8c57bfd 	ldp	x29, x30, [sp], #80
    1ebc:	d65f03c0 	ret

0000000000001ec0 <IndexMapping_Spectra6_T2000_Y8>:
    1ec0:	a9bc7bfd 	stp	x29, x30, [sp, #-64]!
    1ec4:	910003fd 	mov	x29, sp
    1ec8:	f90017a0 	str	x0, [x29, #40]
    1ecc:	f90013a1 	str	x1, [x29, #32]
    1ed0:	b9001fa2 	str	w2, [x29, #28]
    1ed4:	b9001ba3 	str	w3, [x29, #24]
    1ed8:	b9401ba2 	ldr	w2, [x29, #24]
    1edc:	b9401fa1 	ldr	w1, [x29, #28]
    1ee0:	f94017a0 	ldr	x0, [x29, #40]
    1ee4:	0f000403 	movi	v3.2s, #0x0
    1ee8:	52a84183 	mov	w3, #0x420c0000            	// #1108082688
    1eec:	1e270062 	fmov	s2, w3
    1ef0:	529999a3 	mov	w3, #0xcccd                	// #52429
    1ef4:	72a7b983 	movk	w3, #0x3dcc, lsl #16
    1ef8:	1e270061 	fmov	s1, w3
    1efc:	1e2f1000 	fmov	s0, #1.500000000000000000e+00
    1f00:	97fffee6 	bl	1a98 <ImageProcess_Spectra6_AIO>
    1f04:	b9003fbf 	str	wzr, [x29, #60]
    1f08:	b9003bbf 	str	wzr, [x29, #56]
    1f0c:	1400004a 	b	2034 <IndexMapping_Spectra6_T2000_Y8+0x174>
    1f10:	b9803fa0 	ldrsw	x0, [x29, #60]
    1f14:	f94017a1 	ldr	x1, [x29, #40]
    1f18:	8b000020 	add	x0, x1, x0
    1f1c:	39400000 	ldrb	w0, [x0]
    1f20:	3900cfa0 	strb	w0, [x29, #51]
    1f24:	b9803fa0 	ldrsw	x0, [x29, #60]
    1f28:	91000400 	add	x0, x0, #0x1
    1f2c:	f94017a1 	ldr	x1, [x29, #40]
    1f30:	8b000020 	add	x0, x1, x0
    1f34:	39400000 	ldrb	w0, [x0]
    1f38:	3900cba0 	strb	w0, [x29, #50]
    1f3c:	b9803fa0 	ldrsw	x0, [x29, #60]
    1f40:	91000800 	add	x0, x0, #0x2
    1f44:	f94017a1 	ldr	x1, [x29, #40]
    1f48:	8b000020 	add	x0, x1, x0
    1f4c:	39400000 	ldrb	w0, [x0]
    1f50:	3900c7a0 	strb	w0, [x29, #49]
    1f54:	b90037bf 	str	wzr, [x29, #52]
    1f58:	1400002e 	b	2010 <IndexMapping_Spectra6_T2000_Y8+0x150>
    1f5c:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    1f60:	91000002 	add	x2, x0, #0x0
    1f64:	b98037a1 	ldrsw	x1, [x29, #52]
    1f68:	aa0103e0 	mov	x0, x1
    1f6c:	d37ff800 	lsl	x0, x0, #1
    1f70:	8b010000 	add	x0, x0, x1
    1f74:	8b000040 	add	x0, x2, x0
    1f78:	39400000 	ldrb	w0, [x0]
    1f7c:	3940cfa1 	ldrb	w1, [x29, #51]
    1f80:	6b00003f 	cmp	w1, w0
    1f84:	54000401 	b.ne	2004 <IndexMapping_Spectra6_T2000_Y8+0x144>  // b.any
    1f88:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    1f8c:	91000002 	add	x2, x0, #0x0
    1f90:	b98037a1 	ldrsw	x1, [x29, #52]
    1f94:	aa0103e0 	mov	x0, x1
    1f98:	d37ff800 	lsl	x0, x0, #1
    1f9c:	8b010000 	add	x0, x0, x1
    1fa0:	8b000040 	add	x0, x2, x0
    1fa4:	39400400 	ldrb	w0, [x0, #1]
    1fa8:	3940cba1 	ldrb	w1, [x29, #50]
    1fac:	6b00003f 	cmp	w1, w0
    1fb0:	540002a1 	b.ne	2004 <IndexMapping_Spectra6_T2000_Y8+0x144>  // b.any
    1fb4:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    1fb8:	91000002 	add	x2, x0, #0x0
    1fbc:	b98037a1 	ldrsw	x1, [x29, #52]
    1fc0:	aa0103e0 	mov	x0, x1
    1fc4:	d37ff800 	lsl	x0, x0, #1
    1fc8:	8b010000 	add	x0, x0, x1
    1fcc:	8b000040 	add	x0, x2, x0
    1fd0:	39400800 	ldrb	w0, [x0, #2]
    1fd4:	3940c7a1 	ldrb	w1, [x29, #49]
    1fd8:	6b00003f 	cmp	w1, w0
    1fdc:	54000141 	b.ne	2004 <IndexMapping_Spectra6_T2000_Y8+0x144>  // b.any
    1fe0:	b9803ba0 	ldrsw	x0, [x29, #56]
    1fe4:	f94013a1 	ldr	x1, [x29, #32]
    1fe8:	8b000020 	add	x0, x1, x0
    1fec:	90000001 	adrp	x1, 20 <SWAP_RGB_BGR+0x20>
    1ff0:	91000022 	add	x2, x1, #0x0
    1ff4:	b98037a1 	ldrsw	x1, [x29, #52]
    1ff8:	38616841 	ldrb	w1, [x2, x1]
    1ffc:	39000001 	strb	w1, [x0]
    2000:	14000007 	b	201c <IndexMapping_Spectra6_T2000_Y8+0x15c>
    2004:	b94037a0 	ldr	w0, [x29, #52]
    2008:	11000400 	add	w0, w0, #0x1
    200c:	b90037a0 	str	w0, [x29, #52]
    2010:	b94037a0 	ldr	w0, [x29, #52]
    2014:	7100141f 	cmp	w0, #0x5
    2018:	54fffa2d 	b.le	1f5c <IndexMapping_Spectra6_T2000_Y8+0x9c>
    201c:	b9403fa0 	ldr	w0, [x29, #60]
    2020:	11000c00 	add	w0, w0, #0x3
    2024:	b9003fa0 	str	w0, [x29, #60]
    2028:	b9403ba0 	ldr	w0, [x29, #56]
    202c:	11000400 	add	w0, w0, #0x1
    2030:	b9003ba0 	str	w0, [x29, #56]
    2034:	b9401fa1 	ldr	w1, [x29, #28]
    2038:	b9401ba0 	ldr	w0, [x29, #24]
    203c:	1b007c20 	mul	w0, w1, w0
    2040:	b9403ba1 	ldr	w1, [x29, #56]
    2044:	6b00003f 	cmp	w1, w0
    2048:	54fff64b 	b.lt	1f10 <IndexMapping_Spectra6_T2000_Y8+0x50>  // b.tstop
    204c:	d503201f 	nop
    2050:	a8c47bfd 	ldp	x29, x30, [sp], #64
    2054:	d65f03c0 	ret

0000000000002058 <find_nearest_color_T2001>:
    2058:	d100c3ff 	sub	sp, sp, #0x30
    205c:	b9000fe0 	str	w0, [sp, #12]
    2060:	b9000be1 	str	w1, [sp, #8]
    2064:	b90007e2 	str	w2, [sp, #4]
    2068:	12b00000 	mov	w0, #0x7fffffff            	// #2147483647
    206c:	b9002fe0 	str	w0, [sp, #44]
    2070:	b9002bff 	str	wzr, [sp, #40]
    2074:	b90027ff 	str	wzr, [sp, #36]
    2078:	1400003c 	b	2168 <find_nearest_color_T2001+0x110>
    207c:	b9400fe0 	ldr	w0, [sp, #12]
    2080:	12001c02 	and	w2, w0, #0xff
    2084:	90000000 	adrp	x0, 28 <SWAP_RGB_BGR+0x28>
    2088:	91000003 	add	x3, x0, #0x0
    208c:	b98027e1 	ldrsw	x1, [sp, #36]
    2090:	aa0103e0 	mov	x0, x1
    2094:	d37ff800 	lsl	x0, x0, #1
    2098:	8b010000 	add	x0, x0, x1
    209c:	8b000060 	add	x0, x3, x0
    20a0:	39400000 	ldrb	w0, [x0]
    20a4:	4b000040 	sub	w0, w2, w0
    20a8:	b90023e0 	str	w0, [sp, #32]
    20ac:	b9400be0 	ldr	w0, [sp, #8]
    20b0:	12001c02 	and	w2, w0, #0xff
    20b4:	90000000 	adrp	x0, 28 <SWAP_RGB_BGR+0x28>
    20b8:	91000003 	add	x3, x0, #0x0
    20bc:	b98027e1 	ldrsw	x1, [sp, #36]
    20c0:	aa0103e0 	mov	x0, x1
    20c4:	d37ff800 	lsl	x0, x0, #1
    20c8:	8b010000 	add	x0, x0, x1
    20cc:	8b000060 	add	x0, x3, x0
    20d0:	39400400 	ldrb	w0, [x0, #1]
    20d4:	4b000040 	sub	w0, w2, w0
    20d8:	b9001fe0 	str	w0, [sp, #28]
    20dc:	b94007e0 	ldr	w0, [sp, #4]
    20e0:	12001c02 	and	w2, w0, #0xff
    20e4:	90000000 	adrp	x0, 28 <SWAP_RGB_BGR+0x28>
    20e8:	91000003 	add	x3, x0, #0x0
    20ec:	b98027e1 	ldrsw	x1, [sp, #36]
    20f0:	aa0103e0 	mov	x0, x1
    20f4:	d37ff800 	lsl	x0, x0, #1
    20f8:	8b010000 	add	x0, x0, x1
    20fc:	8b000060 	add	x0, x3, x0
    2100:	39400800 	ldrb	w0, [x0, #2]
    2104:	4b000040 	sub	w0, w2, w0
    2108:	b9001be0 	str	w0, [sp, #24]
    210c:	b94023e1 	ldr	w1, [sp, #32]
    2110:	b94023e0 	ldr	w0, [sp, #32]
    2114:	1b007c21 	mul	w1, w1, w0
    2118:	b9401fe2 	ldr	w2, [sp, #28]
    211c:	b9401fe0 	ldr	w0, [sp, #28]
    2120:	1b007c40 	mul	w0, w2, w0
    2124:	0b000021 	add	w1, w1, w0
    2128:	b9401be2 	ldr	w2, [sp, #24]
    212c:	b9401be0 	ldr	w0, [sp, #24]
    2130:	1b007c40 	mul	w0, w2, w0
    2134:	0b000020 	add	w0, w1, w0
    2138:	b90017e0 	str	w0, [sp, #20]
    213c:	b94017e1 	ldr	w1, [sp, #20]
    2140:	b9402fe0 	ldr	w0, [sp, #44]
    2144:	6b00003f 	cmp	w1, w0
    2148:	540000aa 	b.ge	215c <find_nearest_color_T2001+0x104>  // b.tcont
    214c:	b94017e0 	ldr	w0, [sp, #20]
    2150:	b9002fe0 	str	w0, [sp, #44]
    2154:	b94027e0 	ldr	w0, [sp, #36]
    2158:	b9002be0 	str	w0, [sp, #40]
    215c:	b94027e0 	ldr	w0, [sp, #36]
    2160:	11000400 	add	w0, w0, #0x1
    2164:	b90027e0 	str	w0, [sp, #36]
    2168:	b94027e0 	ldr	w0, [sp, #36]
    216c:	7100241f 	cmp	w0, #0x9
    2170:	54fff86d 	b.le	207c <find_nearest_color_T2001+0x24>
    2174:	b9402be0 	ldr	w0, [sp, #40]
    2178:	12001c00 	and	w0, w0, #0xff
    217c:	9100c3ff 	add	sp, sp, #0x30
    2180:	d65f03c0 	ret

0000000000002184 <ImageProcess_Dither_T2001>:
    2184:	a9b17bfd 	stp	x29, x30, [sp, #-240]!
    2188:	910003fd 	mov	x29, sp
    218c:	f9000fa0 	str	x0, [x29, #24]
    2190:	b90017a1 	str	w1, [x29, #20]
    2194:	b90013a2 	str	w2, [x29, #16]
    2198:	b900efbf 	str	wzr, [x29, #236]
    219c:	b98017a1 	ldrsw	x1, [x29, #20]
    21a0:	aa0103e0 	mov	x0, x1
    21a4:	d37ff800 	lsl	x0, x0, #1
    21a8:	8b010000 	add	x0, x0, x1
    21ac:	d37ff800 	lsl	x0, x0, #1
    21b0:	94000000 	bl	0 <malloc>
    21b4:	f9004fa0 	str	x0, [x29, #152]
    21b8:	b98017a1 	ldrsw	x1, [x29, #20]
    21bc:	aa0103e0 	mov	x0, x1
    21c0:	d37ff800 	lsl	x0, x0, #1
    21c4:	8b010000 	add	x0, x0, x1
    21c8:	d37ff800 	lsl	x0, x0, #1
    21cc:	94000000 	bl	0 <malloc>
    21d0:	f90053a0 	str	x0, [x29, #160]
    21d4:	b98017a1 	ldrsw	x1, [x29, #20]
    21d8:	aa0103e0 	mov	x0, x1
    21dc:	d37ff800 	lsl	x0, x0, #1
    21e0:	8b010000 	add	x0, x0, x1
    21e4:	d37ff800 	lsl	x0, x0, #1
    21e8:	94000000 	bl	0 <malloc>
    21ec:	f90057a0 	str	x0, [x29, #168]
    21f0:	f9404fa3 	ldr	x3, [x29, #152]
    21f4:	b98017a1 	ldrsw	x1, [x29, #20]
    21f8:	aa0103e0 	mov	x0, x1
    21fc:	d37ff800 	lsl	x0, x0, #1
    2200:	8b010000 	add	x0, x0, x1
    2204:	d37ff800 	lsl	x0, x0, #1
    2208:	aa0003e2 	mov	x2, x0
    220c:	52800001 	mov	w1, #0x0                   	// #0
    2210:	aa0303e0 	mov	x0, x3
    2214:	94000000 	bl	0 <memset>
    2218:	f94053a3 	ldr	x3, [x29, #160]
    221c:	b98017a1 	ldrsw	x1, [x29, #20]
    2220:	aa0103e0 	mov	x0, x1
    2224:	d37ff800 	lsl	x0, x0, #1
    2228:	8b010000 	add	x0, x0, x1
    222c:	d37ff800 	lsl	x0, x0, #1
    2230:	aa0003e2 	mov	x2, x0
    2234:	52800001 	mov	w1, #0x0                   	// #0
    2238:	aa0303e0 	mov	x0, x3
    223c:	94000000 	bl	0 <memset>
    2240:	f94057a3 	ldr	x3, [x29, #168]
    2244:	b98017a1 	ldrsw	x1, [x29, #20]
    2248:	aa0103e0 	mov	x0, x1
    224c:	d37ff800 	lsl	x0, x0, #1
    2250:	8b010000 	add	x0, x0, x1
    2254:	d37ff800 	lsl	x0, x0, #1
    2258:	aa0003e2 	mov	x2, x0
    225c:	52800001 	mov	w1, #0x0                   	// #0
    2260:	aa0303e0 	mov	x0, x3
    2264:	94000000 	bl	0 <memset>
    2268:	f9404fa0 	ldr	x0, [x29, #152]
    226c:	f100001f 	cmp	x0, #0x0
    2270:	540000a1 	b.ne	2284 <ImageProcess_Dither_T2001+0x100>  // b.any
    2274:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    2278:	91000000 	add	x0, x0, #0x0
    227c:	94000000 	bl	0 <puts>
    2280:	14000197 	b	28dc <ImageProcess_Dither_T2001+0x758>
    2284:	f94053a0 	ldr	x0, [x29, #160]
    2288:	f100001f 	cmp	x0, #0x0
    228c:	540000a1 	b.ne	22a0 <ImageProcess_Dither_T2001+0x11c>  // b.any
    2290:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    2294:	91000000 	add	x0, x0, #0x0
    2298:	94000000 	bl	0 <puts>
    229c:	14000190 	b	28dc <ImageProcess_Dither_T2001+0x758>
    22a0:	f94057a0 	ldr	x0, [x29, #168]
    22a4:	f100001f 	cmp	x0, #0x0
    22a8:	540000a1 	b.ne	22bc <ImageProcess_Dither_T2001+0x138>  // b.any
    22ac:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    22b0:	91000000 	add	x0, x0, #0x0
    22b4:	94000000 	bl	0 <puts>
    22b8:	14000189 	b	28dc <ImageProcess_Dither_T2001+0x758>
    22bc:	b900ebbf 	str	wzr, [x29, #232]
    22c0:	1400017d 	b	28b4 <ImageProcess_Dither_T2001+0x730>
    22c4:	b900e7bf 	str	wzr, [x29, #228]
    22c8:	14000154 	b	2818 <ImageProcess_Dither_T2001+0x694>
    22cc:	b980efa0 	ldrsw	x0, [x29, #236]
    22d0:	f9400fa1 	ldr	x1, [x29, #24]
    22d4:	8b000020 	add	x0, x1, x0
    22d8:	39400000 	ldrb	w0, [x0]
    22dc:	2a0003e3 	mov	w3, w0
    22e0:	f9404fa2 	ldr	x2, [x29, #152]
    22e4:	b980e7a1 	ldrsw	x1, [x29, #228]
    22e8:	aa0103e0 	mov	x0, x1
    22ec:	d37ff800 	lsl	x0, x0, #1
    22f0:	8b010000 	add	x0, x0, x1
    22f4:	d37ff800 	lsl	x0, x0, #1
    22f8:	8b000040 	add	x0, x2, x0
    22fc:	79c00000 	ldrsh	w0, [x0]
    2300:	0b000060 	add	w0, w3, w0
    2304:	b900dfa0 	str	w0, [x29, #220]
    2308:	b980efa0 	ldrsw	x0, [x29, #236]
    230c:	91000400 	add	x0, x0, #0x1
    2310:	f9400fa1 	ldr	x1, [x29, #24]
    2314:	8b000020 	add	x0, x1, x0
    2318:	39400000 	ldrb	w0, [x0]
    231c:	2a0003e3 	mov	w3, w0
    2320:	f9404fa2 	ldr	x2, [x29, #152]
    2324:	b980e7a1 	ldrsw	x1, [x29, #228]
    2328:	aa0103e0 	mov	x0, x1
    232c:	d37ff800 	lsl	x0, x0, #1
    2330:	8b010000 	add	x0, x0, x1
    2334:	d37ff800 	lsl	x0, x0, #1
    2338:	8b000040 	add	x0, x2, x0
    233c:	79c00400 	ldrsh	w0, [x0, #2]
    2340:	0b000060 	add	w0, w3, w0
    2344:	b900dba0 	str	w0, [x29, #216]
    2348:	b980efa0 	ldrsw	x0, [x29, #236]
    234c:	91000800 	add	x0, x0, #0x2
    2350:	f9400fa1 	ldr	x1, [x29, #24]
    2354:	8b000020 	add	x0, x1, x0
    2358:	39400000 	ldrb	w0, [x0]
    235c:	2a0003e3 	mov	w3, w0
    2360:	f9404fa2 	ldr	x2, [x29, #152]
    2364:	b980e7a1 	ldrsw	x1, [x29, #228]
    2368:	aa0103e0 	mov	x0, x1
    236c:	d37ff800 	lsl	x0, x0, #1
    2370:	8b010000 	add	x0, x0, x1
    2374:	d37ff800 	lsl	x0, x0, #1
    2378:	8b000040 	add	x0, x2, x0
    237c:	79c00800 	ldrsh	w0, [x0, #4]
    2380:	0b000060 	add	w0, w3, w0
    2384:	b900d7a0 	str	w0, [x29, #212]
    2388:	b940dfa0 	ldr	w0, [x29, #220]
    238c:	1e220000 	scvtf	s0, w0
    2390:	0f000401 	movi	v1.2s, #0x0
    2394:	94000000 	bl	0 <fmaxf>
    2398:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    239c:	1e270001 	fmov	s1, w0
    23a0:	94000000 	bl	0 <fminf>
    23a4:	1e380000 	fcvtzs	w0, s0
    23a8:	b900dfa0 	str	w0, [x29, #220]
    23ac:	b940dba0 	ldr	w0, [x29, #216]
    23b0:	1e220000 	scvtf	s0, w0
    23b4:	0f000401 	movi	v1.2s, #0x0
    23b8:	94000000 	bl	0 <fmaxf>
    23bc:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    23c0:	1e270001 	fmov	s1, w0
    23c4:	94000000 	bl	0 <fminf>
    23c8:	1e380000 	fcvtzs	w0, s0
    23cc:	b900dba0 	str	w0, [x29, #216]
    23d0:	b940d7a0 	ldr	w0, [x29, #212]
    23d4:	1e220000 	scvtf	s0, w0
    23d8:	0f000401 	movi	v1.2s, #0x0
    23dc:	94000000 	bl	0 <fmaxf>
    23e0:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    23e4:	1e270001 	fmov	s1, w0
    23e8:	94000000 	bl	0 <fminf>
    23ec:	1e380000 	fcvtzs	w0, s0
    23f0:	b900d7a0 	str	w0, [x29, #212]
    23f4:	b940d7a2 	ldr	w2, [x29, #212]
    23f8:	b940dba1 	ldr	w1, [x29, #216]
    23fc:	b940dfa0 	ldr	w0, [x29, #220]
    2400:	97ffff16 	bl	2058 <find_nearest_color_T2001>
    2404:	39034fa0 	strb	w0, [x29, #211]
    2408:	39434fa1 	ldrb	w1, [x29, #211]
    240c:	90000000 	adrp	x0, 28 <SWAP_RGB_BGR+0x28>
    2410:	91000002 	add	x2, x0, #0x0
    2414:	93407c21 	sxtw	x1, w1
    2418:	aa0103e0 	mov	x0, x1
    241c:	d37ff800 	lsl	x0, x0, #1
    2420:	8b010000 	add	x0, x0, x1
    2424:	8b000040 	add	x0, x2, x0
    2428:	39400000 	ldrb	w0, [x0]
    242c:	39034ba0 	strb	w0, [x29, #210]
    2430:	39434fa1 	ldrb	w1, [x29, #211]
    2434:	90000000 	adrp	x0, 28 <SWAP_RGB_BGR+0x28>
    2438:	91000002 	add	x2, x0, #0x0
    243c:	93407c21 	sxtw	x1, w1
    2440:	aa0103e0 	mov	x0, x1
    2444:	d37ff800 	lsl	x0, x0, #1
    2448:	8b010000 	add	x0, x0, x1
    244c:	8b000040 	add	x0, x2, x0
    2450:	39400400 	ldrb	w0, [x0, #1]
    2454:	390347a0 	strb	w0, [x29, #209]
    2458:	39434fa1 	ldrb	w1, [x29, #211]
    245c:	90000000 	adrp	x0, 28 <SWAP_RGB_BGR+0x28>
    2460:	91000002 	add	x2, x0, #0x0
    2464:	93407c21 	sxtw	x1, w1
    2468:	aa0103e0 	mov	x0, x1
    246c:	d37ff800 	lsl	x0, x0, #1
    2470:	8b010000 	add	x0, x0, x1
    2474:	8b000040 	add	x0, x2, x0
    2478:	39400800 	ldrb	w0, [x0, #2]
    247c:	390343a0 	strb	w0, [x29, #208]
    2480:	b980efa0 	ldrsw	x0, [x29, #236]
    2484:	f9400fa1 	ldr	x1, [x29, #24]
    2488:	8b000020 	add	x0, x1, x0
    248c:	39434ba1 	ldrb	w1, [x29, #210]
    2490:	39000001 	strb	w1, [x0]
    2494:	b980efa0 	ldrsw	x0, [x29, #236]
    2498:	91000400 	add	x0, x0, #0x1
    249c:	f9400fa1 	ldr	x1, [x29, #24]
    24a0:	8b000020 	add	x0, x1, x0
    24a4:	394347a1 	ldrb	w1, [x29, #209]
    24a8:	39000001 	strb	w1, [x0]
    24ac:	b980efa0 	ldrsw	x0, [x29, #236]
    24b0:	91000800 	add	x0, x0, #0x2
    24b4:	f9400fa1 	ldr	x1, [x29, #24]
    24b8:	8b000020 	add	x0, x1, x0
    24bc:	394343a1 	ldrb	w1, [x29, #208]
    24c0:	39000001 	strb	w1, [x0]
    24c4:	b940efa0 	ldr	w0, [x29, #236]
    24c8:	11000c00 	add	w0, w0, #0x3
    24cc:	b900efa0 	str	w0, [x29, #236]
    24d0:	39434ba0 	ldrb	w0, [x29, #210]
    24d4:	b940dfa1 	ldr	w1, [x29, #220]
    24d8:	4b000020 	sub	w0, w1, w0
    24dc:	b900cfa0 	str	w0, [x29, #204]
    24e0:	394347a0 	ldrb	w0, [x29, #209]
    24e4:	b940dba1 	ldr	w1, [x29, #216]
    24e8:	4b000020 	sub	w0, w1, w0
    24ec:	b900cba0 	str	w0, [x29, #200]
    24f0:	394343a0 	ldrb	w0, [x29, #208]
    24f4:	b940d7a1 	ldr	w1, [x29, #212]
    24f8:	4b000020 	sub	w0, w1, w0
    24fc:	b900c7a0 	str	w0, [x29, #196]
    2500:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    2504:	91000000 	add	x0, x0, #0x0
    2508:	910203a2 	add	x2, x29, #0x80
    250c:	aa0003e3 	mov	x3, x0
    2510:	a9400460 	ldp	x0, x1, [x3]
    2514:	a9000440 	stp	x0, x1, [x2]
    2518:	f9400860 	ldr	x0, [x3, #16]
    251c:	f9000840 	str	x0, [x2, #16]
    2520:	90000000 	adrp	x0, 0 <SWAP_RGB_BGR>
    2524:	91000001 	add	x1, x0, #0x0
    2528:	910083a0 	add	x0, x29, #0x20
    252c:	a9400c22 	ldp	x2, x3, [x1]
    2530:	a9000c02 	stp	x2, x3, [x0]
    2534:	a9410c22 	ldp	x2, x3, [x1, #16]
    2538:	a9010c02 	stp	x2, x3, [x0, #16]
    253c:	a9420c22 	ldp	x2, x3, [x1, #32]
    2540:	a9020c02 	stp	x2, x3, [x0, #32]
    2544:	a9430c22 	ldp	x2, x3, [x1, #48]
    2548:	a9030c02 	stp	x2, x3, [x0, #48]
    254c:	a9440c22 	ldp	x2, x3, [x1, #64]
    2550:	a9040c02 	stp	x2, x3, [x0, #64]
    2554:	a9450821 	ldp	x1, x2, [x1, #80]
    2558:	a9050801 	stp	x1, x2, [x0, #80]
    255c:	b900e3bf 	str	wzr, [x29, #224]
    2560:	140000a8 	b	2800 <ImageProcess_Dither_T2001+0x67c>
    2564:	b940e3a0 	ldr	w0, [x29, #224]
    2568:	531f7800 	lsl	w0, w0, #1
    256c:	93407c00 	sxtw	x0, w0
    2570:	d37ef400 	lsl	x0, x0, #2
    2574:	910083a1 	add	x1, x29, #0x20
    2578:	b8606820 	ldr	w0, [x1, x0]
    257c:	b900c3a0 	str	w0, [x29, #192]
    2580:	b940e3a0 	ldr	w0, [x29, #224]
    2584:	531f7800 	lsl	w0, w0, #1
    2588:	11000400 	add	w0, w0, #0x1
    258c:	93407c00 	sxtw	x0, w0
    2590:	d37ef400 	lsl	x0, x0, #2
    2594:	910083a1 	add	x1, x29, #0x20
    2598:	b8606820 	ldr	w0, [x1, x0]
    259c:	b900bfa0 	str	w0, [x29, #188]
    25a0:	b980e3a0 	ldrsw	x0, [x29, #224]
    25a4:	d37ff800 	lsl	x0, x0, #1
    25a8:	910203a1 	add	x1, x29, #0x80
    25ac:	78e06820 	ldrsh	w0, [x1, x0]
    25b0:	b900bba0 	str	w0, [x29, #184]
    25b4:	b940e7a1 	ldr	w1, [x29, #228]
    25b8:	b940c3a0 	ldr	w0, [x29, #192]
    25bc:	0b000020 	add	w0, w1, w0
    25c0:	7100001f 	cmp	w0, #0x0
    25c4:	5400112b 	b.lt	27e8 <ImageProcess_Dither_T2001+0x664>  // b.tstop
    25c8:	b940e7a1 	ldr	w1, [x29, #228]
    25cc:	b940c3a0 	ldr	w0, [x29, #192]
    25d0:	0b000020 	add	w0, w1, w0
    25d4:	b94017a1 	ldr	w1, [x29, #20]
    25d8:	6b00003f 	cmp	w1, w0
    25dc:	5400106d 	b.le	27e8 <ImageProcess_Dither_T2001+0x664>
    25e0:	b940eba1 	ldr	w1, [x29, #232]
    25e4:	b940bfa0 	ldr	w0, [x29, #188]
    25e8:	0b000020 	add	w0, w1, w0
    25ec:	b94013a1 	ldr	w1, [x29, #16]
    25f0:	6b00003f 	cmp	w1, w0
    25f4:	54000fed 	b.le	27f0 <ImageProcess_Dither_T2001+0x66c>
    25f8:	b940bfa0 	ldr	w0, [x29, #188]
    25fc:	7100001f 	cmp	w0, #0x0
    2600:	54000100 	b.eq	2620 <ImageProcess_Dither_T2001+0x49c>  // b.none
    2604:	b940bfa0 	ldr	w0, [x29, #188]
    2608:	7100041f 	cmp	w0, #0x1
    260c:	54000061 	b.ne	2618 <ImageProcess_Dither_T2001+0x494>  // b.any
    2610:	52800020 	mov	w0, #0x1                   	// #1
    2614:	14000004 	b	2624 <ImageProcess_Dither_T2001+0x4a0>
    2618:	52800040 	mov	w0, #0x2                   	// #2
    261c:	14000002 	b	2624 <ImageProcess_Dither_T2001+0x4a0>
    2620:	52800000 	mov	w0, #0x0                   	// #0
    2624:	b900b7a0 	str	w0, [x29, #180]
    2628:	b980b7a0 	ldrsw	x0, [x29, #180]
    262c:	d37df000 	lsl	x0, x0, #3
    2630:	910263a1 	add	x1, x29, #0x98
    2634:	f8606822 	ldr	x2, [x1, x0]
    2638:	b940e7a1 	ldr	w1, [x29, #228]
    263c:	b940c3a0 	ldr	w0, [x29, #192]
    2640:	0b000020 	add	w0, w1, w0
    2644:	93407c01 	sxtw	x1, w0
    2648:	aa0103e0 	mov	x0, x1
    264c:	d37ff800 	lsl	x0, x0, #1
    2650:	8b010000 	add	x0, x0, x1
    2654:	d37ff800 	lsl	x0, x0, #1
    2658:	8b000040 	add	x0, x2, x0
    265c:	79c00000 	ldrsh	w0, [x0]
    2660:	12003c01 	and	w1, w0, #0xffff
    2664:	b940cfa2 	ldr	w2, [x29, #204]
    2668:	b940bba0 	ldr	w0, [x29, #184]
    266c:	1b007c40 	mul	w0, w2, w0
    2670:	13067c00 	asr	w0, w0, #6
    2674:	12003c00 	and	w0, w0, #0xffff
    2678:	0b000020 	add	w0, w1, w0
    267c:	12003c03 	and	w3, w0, #0xffff
    2680:	b980b7a0 	ldrsw	x0, [x29, #180]
    2684:	d37df000 	lsl	x0, x0, #3
    2688:	910263a1 	add	x1, x29, #0x98
    268c:	f8606822 	ldr	x2, [x1, x0]
    2690:	b940e7a1 	ldr	w1, [x29, #228]
    2694:	b940c3a0 	ldr	w0, [x29, #192]
    2698:	0b000020 	add	w0, w1, w0
    269c:	93407c01 	sxtw	x1, w0
    26a0:	aa0103e0 	mov	x0, x1
    26a4:	d37ff800 	lsl	x0, x0, #1
    26a8:	8b010000 	add	x0, x0, x1
    26ac:	d37ff800 	lsl	x0, x0, #1
    26b0:	8b000040 	add	x0, x2, x0
    26b4:	13003c61 	sxth	w1, w3
    26b8:	79000001 	strh	w1, [x0]
    26bc:	b980b7a0 	ldrsw	x0, [x29, #180]
    26c0:	d37df000 	lsl	x0, x0, #3
    26c4:	910263a1 	add	x1, x29, #0x98
    26c8:	f8606822 	ldr	x2, [x1, x0]
    26cc:	b940e7a1 	ldr	w1, [x29, #228]
    26d0:	b940c3a0 	ldr	w0, [x29, #192]
    26d4:	0b000020 	add	w0, w1, w0
    26d8:	93407c01 	sxtw	x1, w0
    26dc:	aa0103e0 	mov	x0, x1
    26e0:	d37ff800 	lsl	x0, x0, #1
    26e4:	8b010000 	add	x0, x0, x1
    26e8:	d37ff800 	lsl	x0, x0, #1
    26ec:	8b000040 	add	x0, x2, x0
    26f0:	79c00400 	ldrsh	w0, [x0, #2]
    26f4:	12003c01 	and	w1, w0, #0xffff
    26f8:	b940cba2 	ldr	w2, [x29, #200]
    26fc:	b940bba0 	ldr	w0, [x29, #184]
    2700:	1b007c40 	mul	w0, w2, w0
    2704:	13067c00 	asr	w0, w0, #6
    2708:	12003c00 	and	w0, w0, #0xffff
    270c:	0b000020 	add	w0, w1, w0
    2710:	12003c03 	and	w3, w0, #0xffff
    2714:	b980b7a0 	ldrsw	x0, [x29, #180]
    2718:	d37df000 	lsl	x0, x0, #3
    271c:	910263a1 	add	x1, x29, #0x98
    2720:	f8606822 	ldr	x2, [x1, x0]
    2724:	b940e7a1 	ldr	w1, [x29, #228]
    2728:	b940c3a0 	ldr	w0, [x29, #192]
    272c:	0b000020 	add	w0, w1, w0
    2730:	93407c01 	sxtw	x1, w0
    2734:	aa0103e0 	mov	x0, x1
    2738:	d37ff800 	lsl	x0, x0, #1
    273c:	8b010000 	add	x0, x0, x1
    2740:	d37ff800 	lsl	x0, x0, #1
    2744:	8b000040 	add	x0, x2, x0
    2748:	13003c61 	sxth	w1, w3
    274c:	79000401 	strh	w1, [x0, #2]
    2750:	b980b7a0 	ldrsw	x0, [x29, #180]
    2754:	d37df000 	lsl	x0, x0, #3
    2758:	910263a1 	add	x1, x29, #0x98
    275c:	f8606822 	ldr	x2, [x1, x0]
    2760:	b940e7a1 	ldr	w1, [x29, #228]
    2764:	b940c3a0 	ldr	w0, [x29, #192]
    2768:	0b000020 	add	w0, w1, w0
    276c:	93407c01 	sxtw	x1, w0
    2770:	aa0103e0 	mov	x0, x1
    2774:	d37ff800 	lsl	x0, x0, #1
    2778:	8b010000 	add	x0, x0, x1
    277c:	d37ff800 	lsl	x0, x0, #1
    2780:	8b000040 	add	x0, x2, x0
    2784:	79c00800 	ldrsh	w0, [x0, #4]
    2788:	12003c01 	and	w1, w0, #0xffff
    278c:	b940c7a2 	ldr	w2, [x29, #196]
    2790:	b940bba0 	ldr	w0, [x29, #184]
    2794:	1b007c40 	mul	w0, w2, w0
    2798:	13067c00 	asr	w0, w0, #6
    279c:	12003c00 	and	w0, w0, #0xffff
    27a0:	0b000020 	add	w0, w1, w0
    27a4:	12003c03 	and	w3, w0, #0xffff
    27a8:	b980b7a0 	ldrsw	x0, [x29, #180]
    27ac:	d37df000 	lsl	x0, x0, #3
    27b0:	910263a1 	add	x1, x29, #0x98
    27b4:	f8606822 	ldr	x2, [x1, x0]
    27b8:	b940e7a1 	ldr	w1, [x29, #228]
    27bc:	b940c3a0 	ldr	w0, [x29, #192]
    27c0:	0b000020 	add	w0, w1, w0
    27c4:	93407c01 	sxtw	x1, w0
    27c8:	aa0103e0 	mov	x0, x1
    27cc:	d37ff800 	lsl	x0, x0, #1
    27d0:	8b010000 	add	x0, x0, x1
    27d4:	d37ff800 	lsl	x0, x0, #1
    27d8:	8b000040 	add	x0, x2, x0
    27dc:	13003c61 	sxth	w1, w3
    27e0:	79000801 	strh	w1, [x0, #4]
    27e4:	14000004 	b	27f4 <ImageProcess_Dither_T2001+0x670>
    27e8:	d503201f 	nop
    27ec:	14000002 	b	27f4 <ImageProcess_Dither_T2001+0x670>
    27f0:	d503201f 	nop
    27f4:	b940e3a0 	ldr	w0, [x29, #224]
    27f8:	11000400 	add	w0, w0, #0x1
    27fc:	b900e3a0 	str	w0, [x29, #224]
    2800:	b940e3a0 	ldr	w0, [x29, #224]
    2804:	71002c1f 	cmp	w0, #0xb
    2808:	54ffeaed 	b.le	2564 <ImageProcess_Dither_T2001+0x3e0>
    280c:	b940e7a0 	ldr	w0, [x29, #228]
    2810:	11000400 	add	w0, w0, #0x1
    2814:	b900e7a0 	str	w0, [x29, #228]
    2818:	b940e7a1 	ldr	w1, [x29, #228]
    281c:	b94017a0 	ldr	w0, [x29, #20]
    2820:	6b00003f 	cmp	w1, w0
    2824:	54ffd54b 	b.lt	22cc <ImageProcess_Dither_T2001+0x148>  // b.tstop
    2828:	f9404fa3 	ldr	x3, [x29, #152]
    282c:	f94053a4 	ldr	x4, [x29, #160]
    2830:	b98017a1 	ldrsw	x1, [x29, #20]
    2834:	aa0103e0 	mov	x0, x1
    2838:	d37ff800 	lsl	x0, x0, #1
    283c:	8b010000 	add	x0, x0, x1
    2840:	d37ff800 	lsl	x0, x0, #1
    2844:	aa0003e2 	mov	x2, x0
    2848:	aa0403e1 	mov	x1, x4
    284c:	aa0303e0 	mov	x0, x3
    2850:	94000000 	bl	0 <memcpy>
    2854:	f94053a3 	ldr	x3, [x29, #160]
    2858:	f94057a4 	ldr	x4, [x29, #168]
    285c:	b98017a1 	ldrsw	x1, [x29, #20]
    2860:	aa0103e0 	mov	x0, x1
    2864:	d37ff800 	lsl	x0, x0, #1
    2868:	8b010000 	add	x0, x0, x1
    286c:	d37ff800 	lsl	x0, x0, #1
    2870:	aa0003e2 	mov	x2, x0
    2874:	aa0403e1 	mov	x1, x4
    2878:	aa0303e0 	mov	x0, x3
    287c:	94000000 	bl	0 <memcpy>
    2880:	f94057a3 	ldr	x3, [x29, #168]
    2884:	b98017a1 	ldrsw	x1, [x29, #20]
    2888:	aa0103e0 	mov	x0, x1
    288c:	d37ff800 	lsl	x0, x0, #1
    2890:	8b010000 	add	x0, x0, x1
    2894:	d37ff800 	lsl	x0, x0, #1
    2898:	aa0003e2 	mov	x2, x0
    289c:	52800001 	mov	w1, #0x0                   	// #0
    28a0:	aa0303e0 	mov	x0, x3
    28a4:	94000000 	bl	0 <memset>
    28a8:	b940eba0 	ldr	w0, [x29, #232]
    28ac:	11000400 	add	w0, w0, #0x1
    28b0:	b900eba0 	str	w0, [x29, #232]
    28b4:	b940eba1 	ldr	w1, [x29, #232]
    28b8:	b94013a0 	ldr	w0, [x29, #16]
    28bc:	6b00003f 	cmp	w1, w0
    28c0:	54ffd02b 	b.lt	22c4 <ImageProcess_Dither_T2001+0x140>  // b.tstop
    28c4:	f9404fa0 	ldr	x0, [x29, #152]
    28c8:	94000000 	bl	0 <free>
    28cc:	f94053a0 	ldr	x0, [x29, #160]
    28d0:	94000000 	bl	0 <free>
    28d4:	f94057a0 	ldr	x0, [x29, #168]
    28d8:	94000000 	bl	0 <free>
    28dc:	a8cf7bfd 	ldp	x29, x30, [sp], #240
    28e0:	d65f03c0 	ret

00000000000028e4 <ImageProcess_Spectra6_T2001>:
    28e4:	a9bd7bfd 	stp	x29, x30, [sp, #-48]!
    28e8:	910003fd 	mov	x29, sp
    28ec:	bd002fa0 	str	s0, [x29, #44]
    28f0:	bd002ba1 	str	s1, [x29, #40]
    28f4:	bd0027a2 	str	s2, [x29, #36]
    28f8:	bd0023a3 	str	s3, [x29, #32]
    28fc:	f9000fa0 	str	x0, [x29, #24]
    2900:	b90017a1 	str	w1, [x29, #20]
    2904:	b90013a2 	str	w2, [x29, #16]
    2908:	b94013a2 	ldr	w2, [x29, #16]
    290c:	b94017a1 	ldr	w1, [x29, #20]
    2910:	f9400fa0 	ldr	x0, [x29, #24]
    2914:	bd4023a3 	ldr	s3, [x29, #32]
    2918:	bd4027a2 	ldr	s2, [x29, #36]
    291c:	bd402ba1 	ldr	s1, [x29, #40]
    2920:	bd402fa0 	ldr	s0, [x29, #44]
    2924:	94000000 	bl	1180 <ImageProcess_ColorEnhace>
    2928:	b94013a2 	ldr	w2, [x29, #16]
    292c:	b94017a1 	ldr	w1, [x29, #20]
    2930:	f9400fa0 	ldr	x0, [x29, #24]
    2934:	97fffe14 	bl	2184 <ImageProcess_Dither_T2001>
    2938:	d503201f 	nop
    293c:	a8c37bfd 	ldp	x29, x30, [sp], #48
    2940:	d65f03c0 	ret

0000000000002944 <IndexMapping_Spectra6_T2001_Y8_4bit>:
    2944:	a9bc7bfd 	stp	x29, x30, [sp, #-64]!
    2948:	910003fd 	mov	x29, sp
    294c:	f90017a0 	str	x0, [x29, #40]
    2950:	f90013a1 	str	x1, [x29, #32]
    2954:	b9001fa2 	str	w2, [x29, #28]
    2958:	b9001ba3 	str	w3, [x29, #24]
    295c:	b9401ba2 	ldr	w2, [x29, #24]
    2960:	b9401fa1 	ldr	w1, [x29, #28]
    2964:	f94017a0 	ldr	x0, [x29, #40]
    2968:	0f000403 	movi	v3.2s, #0x0
    296c:	52a84183 	mov	w3, #0x420c0000            	// #1108082688
    2970:	1e270062 	fmov	s2, w3
    2974:	529999a3 	mov	w3, #0xcccd                	// #52429
    2978:	72a7b983 	movk	w3, #0x3dcc, lsl #16
    297c:	1e270061 	fmov	s1, w3
    2980:	1e2f1000 	fmov	s0, #1.500000000000000000e+00
    2984:	97ffffd8 	bl	28e4 <ImageProcess_Spectra6_T2001>
    2988:	b9003fbf 	str	wzr, [x29, #60]
    298c:	b9003bbf 	str	wzr, [x29, #56]
    2990:	1400004a 	b	2ab8 <IndexMapping_Spectra6_T2001_Y8_4bit+0x174>
    2994:	b9803fa0 	ldrsw	x0, [x29, #60]
    2998:	f94017a1 	ldr	x1, [x29, #40]
    299c:	8b000020 	add	x0, x1, x0
    29a0:	39400000 	ldrb	w0, [x0]
    29a4:	3900cfa0 	strb	w0, [x29, #51]
    29a8:	b9803fa0 	ldrsw	x0, [x29, #60]
    29ac:	91000400 	add	x0, x0, #0x1
    29b0:	f94017a1 	ldr	x1, [x29, #40]
    29b4:	8b000020 	add	x0, x1, x0
    29b8:	39400000 	ldrb	w0, [x0]
    29bc:	3900cba0 	strb	w0, [x29, #50]
    29c0:	b9803fa0 	ldrsw	x0, [x29, #60]
    29c4:	91000800 	add	x0, x0, #0x2
    29c8:	f94017a1 	ldr	x1, [x29, #40]
    29cc:	8b000020 	add	x0, x1, x0
    29d0:	39400000 	ldrb	w0, [x0]
    29d4:	3900c7a0 	strb	w0, [x29, #49]
    29d8:	b90037bf 	str	wzr, [x29, #52]
    29dc:	1400002e 	b	2a94 <IndexMapping_Spectra6_T2001_Y8_4bit+0x150>
    29e0:	90000000 	adrp	x0, 28 <SWAP_RGB_BGR+0x28>
    29e4:	91000002 	add	x2, x0, #0x0
    29e8:	b98037a1 	ldrsw	x1, [x29, #52]
    29ec:	aa0103e0 	mov	x0, x1
    29f0:	d37ff800 	lsl	x0, x0, #1
    29f4:	8b010000 	add	x0, x0, x1
    29f8:	8b000040 	add	x0, x2, x0
    29fc:	39400000 	ldrb	w0, [x0]
    2a00:	3940cfa1 	ldrb	w1, [x29, #51]
    2a04:	6b00003f 	cmp	w1, w0
    2a08:	54000401 	b.ne	2a88 <IndexMapping_Spectra6_T2001_Y8_4bit+0x144>  // b.any
    2a0c:	90000000 	adrp	x0, 28 <SWAP_RGB_BGR+0x28>
    2a10:	91000002 	add	x2, x0, #0x0
    2a14:	b98037a1 	ldrsw	x1, [x29, #52]
    2a18:	aa0103e0 	mov	x0, x1
    2a1c:	d37ff800 	lsl	x0, x0, #1
    2a20:	8b010000 	add	x0, x0, x1
    2a24:	8b000040 	add	x0, x2, x0
    2a28:	39400400 	ldrb	w0, [x0, #1]
    2a2c:	3940cba1 	ldrb	w1, [x29, #50]
    2a30:	6b00003f 	cmp	w1, w0
    2a34:	540002a1 	b.ne	2a88 <IndexMapping_Spectra6_T2001_Y8_4bit+0x144>  // b.any
    2a38:	90000000 	adrp	x0, 28 <SWAP_RGB_BGR+0x28>
    2a3c:	91000002 	add	x2, x0, #0x0
    2a40:	b98037a1 	ldrsw	x1, [x29, #52]
    2a44:	aa0103e0 	mov	x0, x1
    2a48:	d37ff800 	lsl	x0, x0, #1
    2a4c:	8b010000 	add	x0, x0, x1
    2a50:	8b000040 	add	x0, x2, x0
    2a54:	39400800 	ldrb	w0, [x0, #2]
    2a58:	3940c7a1 	ldrb	w1, [x29, #49]
    2a5c:	6b00003f 	cmp	w1, w0
    2a60:	54000141 	b.ne	2a88 <IndexMapping_Spectra6_T2001_Y8_4bit+0x144>  // b.any
    2a64:	b9803ba0 	ldrsw	x0, [x29, #56]
    2a68:	f94013a1 	ldr	x1, [x29, #32]
    2a6c:	8b000020 	add	x0, x1, x0
    2a70:	90000001 	adrp	x1, 48 <SWAP_RGB_BGR+0x48>
    2a74:	91000022 	add	x2, x1, #0x0
    2a78:	b98037a1 	ldrsw	x1, [x29, #52]
    2a7c:	38616841 	ldrb	w1, [x2, x1]
    2a80:	39000001 	strb	w1, [x0]
    2a84:	14000007 	b	2aa0 <IndexMapping_Spectra6_T2001_Y8_4bit+0x15c>
    2a88:	b94037a0 	ldr	w0, [x29, #52]
    2a8c:	11000400 	add	w0, w0, #0x1
    2a90:	b90037a0 	str	w0, [x29, #52]
    2a94:	b94037a0 	ldr	w0, [x29, #52]
    2a98:	7100241f 	cmp	w0, #0x9
    2a9c:	54fffa2d 	b.le	29e0 <IndexMapping_Spectra6_T2001_Y8_4bit+0x9c>
    2aa0:	b9403fa0 	ldr	w0, [x29, #60]
    2aa4:	11000c00 	add	w0, w0, #0x3
    2aa8:	b9003fa0 	str	w0, [x29, #60]
    2aac:	b9403ba0 	ldr	w0, [x29, #56]
    2ab0:	11000400 	add	w0, w0, #0x1
    2ab4:	b9003ba0 	str	w0, [x29, #56]
    2ab8:	b9401fa1 	ldr	w1, [x29, #28]
    2abc:	b9401ba0 	ldr	w0, [x29, #24]
    2ac0:	1b007c20 	mul	w0, w1, w0
    2ac4:	b9403ba1 	ldr	w1, [x29, #56]
    2ac8:	6b00003f 	cmp	w1, w0
    2acc:	54fff64b 	b.lt	2994 <IndexMapping_Spectra6_T2001_Y8_4bit+0x50>  // b.tstop
    2ad0:	d503201f 	nop
    2ad4:	a8c47bfd 	ldp	x29, x30, [sp], #64
    2ad8:	d65f03c0 	ret

0000000000002adc <IndexMapping_Spectra6_T2001_Y8_5bit>:
    2adc:	a9bc7bfd 	stp	x29, x30, [sp, #-64]!
    2ae0:	910003fd 	mov	x29, sp
    2ae4:	f90017a0 	str	x0, [x29, #40]
    2ae8:	f90013a1 	str	x1, [x29, #32]
    2aec:	b9001fa2 	str	w2, [x29, #28]
    2af0:	b9001ba3 	str	w3, [x29, #24]
    2af4:	b9401ba2 	ldr	w2, [x29, #24]
    2af8:	b9401fa1 	ldr	w1, [x29, #28]
    2afc:	f94017a0 	ldr	x0, [x29, #40]
    2b00:	0f000403 	movi	v3.2s, #0x0
    2b04:	52a84183 	mov	w3, #0x420c0000            	// #1108082688
    2b08:	1e270062 	fmov	s2, w3
    2b0c:	529999a3 	mov	w3, #0xcccd                	// #52429
    2b10:	72a7b983 	movk	w3, #0x3dcc, lsl #16
    2b14:	1e270061 	fmov	s1, w3
    2b18:	1e2f1000 	fmov	s0, #1.500000000000000000e+00
    2b1c:	97ffff72 	bl	28e4 <ImageProcess_Spectra6_T2001>
    2b20:	b9003fbf 	str	wzr, [x29, #60]
    2b24:	b9003bbf 	str	wzr, [x29, #56]
    2b28:	1400004a 	b	2c50 <IndexMapping_Spectra6_T2001_Y8_5bit+0x174>
    2b2c:	b9803fa0 	ldrsw	x0, [x29, #60]
    2b30:	f94017a1 	ldr	x1, [x29, #40]
    2b34:	8b000020 	add	x0, x1, x0
    2b38:	39400000 	ldrb	w0, [x0]
    2b3c:	3900cfa0 	strb	w0, [x29, #51]
    2b40:	b9803fa0 	ldrsw	x0, [x29, #60]
    2b44:	91000400 	add	x0, x0, #0x1
    2b48:	f94017a1 	ldr	x1, [x29, #40]
    2b4c:	8b000020 	add	x0, x1, x0
    2b50:	39400000 	ldrb	w0, [x0]
    2b54:	3900cba0 	strb	w0, [x29, #50]
    2b58:	b9803fa0 	ldrsw	x0, [x29, #60]
    2b5c:	91000800 	add	x0, x0, #0x2
    2b60:	f94017a1 	ldr	x1, [x29, #40]
    2b64:	8b000020 	add	x0, x1, x0
    2b68:	39400000 	ldrb	w0, [x0]
    2b6c:	3900c7a0 	strb	w0, [x29, #49]
    2b70:	b90037bf 	str	wzr, [x29, #52]
    2b74:	1400002e 	b	2c2c <IndexMapping_Spectra6_T2001_Y8_5bit+0x150>
    2b78:	90000000 	adrp	x0, 28 <SWAP_RGB_BGR+0x28>
    2b7c:	91000002 	add	x2, x0, #0x0
    2b80:	b98037a1 	ldrsw	x1, [x29, #52]
    2b84:	aa0103e0 	mov	x0, x1
    2b88:	d37ff800 	lsl	x0, x0, #1
    2b8c:	8b010000 	add	x0, x0, x1
    2b90:	8b000040 	add	x0, x2, x0
    2b94:	39400000 	ldrb	w0, [x0]
    2b98:	3940cfa1 	ldrb	w1, [x29, #51]
    2b9c:	6b00003f 	cmp	w1, w0
    2ba0:	54000401 	b.ne	2c20 <IndexMapping_Spectra6_T2001_Y8_5bit+0x144>  // b.any
    2ba4:	90000000 	adrp	x0, 28 <SWAP_RGB_BGR+0x28>
    2ba8:	91000002 	add	x2, x0, #0x0
    2bac:	b98037a1 	ldrsw	x1, [x29, #52]
    2bb0:	aa0103e0 	mov	x0, x1
    2bb4:	d37ff800 	lsl	x0, x0, #1
    2bb8:	8b010000 	add	x0, x0, x1
    2bbc:	8b000040 	add	x0, x2, x0
    2bc0:	39400400 	ldrb	w0, [x0, #1]
    2bc4:	3940cba1 	ldrb	w1, [x29, #50]
    2bc8:	6b00003f 	cmp	w1, w0
    2bcc:	540002a1 	b.ne	2c20 <IndexMapping_Spectra6_T2001_Y8_5bit+0x144>  // b.any
    2bd0:	90000000 	adrp	x0, 28 <SWAP_RGB_BGR+0x28>
    2bd4:	91000002 	add	x2, x0, #0x0
    2bd8:	b98037a1 	ldrsw	x1, [x29, #52]
    2bdc:	aa0103e0 	mov	x0, x1
    2be0:	d37ff800 	lsl	x0, x0, #1
    2be4:	8b010000 	add	x0, x0, x1
    2be8:	8b000040 	add	x0, x2, x0
    2bec:	39400800 	ldrb	w0, [x0, #2]
    2bf0:	3940c7a1 	ldrb	w1, [x29, #49]
    2bf4:	6b00003f 	cmp	w1, w0
    2bf8:	54000141 	b.ne	2c20 <IndexMapping_Spectra6_T2001_Y8_5bit+0x144>  // b.any
    2bfc:	b9803ba0 	ldrsw	x0, [x29, #56]
    2c00:	f94013a1 	ldr	x1, [x29, #32]
    2c04:	8b000020 	add	x0, x1, x0
    2c08:	90000001 	adrp	x1, 58 <SWAP_RGB_BGR+0x58>
    2c0c:	91000022 	add	x2, x1, #0x0
    2c10:	b98037a1 	ldrsw	x1, [x29, #52]
    2c14:	38616841 	ldrb	w1, [x2, x1]
    2c18:	39000001 	strb	w1, [x0]
    2c1c:	14000007 	b	2c38 <IndexMapping_Spectra6_T2001_Y8_5bit+0x15c>
    2c20:	b94037a0 	ldr	w0, [x29, #52]
    2c24:	11000400 	add	w0, w0, #0x1
    2c28:	b90037a0 	str	w0, [x29, #52]
    2c2c:	b94037a0 	ldr	w0, [x29, #52]
    2c30:	7100241f 	cmp	w0, #0x9
    2c34:	54fffa2d 	b.le	2b78 <IndexMapping_Spectra6_T2001_Y8_5bit+0x9c>
    2c38:	b9403fa0 	ldr	w0, [x29, #60]
    2c3c:	11000c00 	add	w0, w0, #0x3
    2c40:	b9003fa0 	str	w0, [x29, #60]
    2c44:	b9403ba0 	ldr	w0, [x29, #56]
    2c48:	11000400 	add	w0, w0, #0x1
    2c4c:	b9003ba0 	str	w0, [x29, #56]
    2c50:	b9401fa1 	ldr	w1, [x29, #28]
    2c54:	b9401ba0 	ldr	w0, [x29, #24]
    2c58:	1b007c20 	mul	w0, w1, w0
    2c5c:	b9403ba1 	ldr	w1, [x29, #56]
    2c60:	6b00003f 	cmp	w1, w0
    2c64:	54fff64b 	b.lt	2b2c <IndexMapping_Spectra6_T2001_Y8_5bit+0x50>  // b.tstop
    2c68:	d503201f 	nop
    2c6c:	a8c47bfd 	ldp	x29, x30, [sp], #64
    2c70:	d65f03c0 	ret
