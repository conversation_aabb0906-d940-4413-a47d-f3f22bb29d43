
libimagetools.so:     file format elf64-littleaarch64


Disassembly of section .init:

0000000000000c58 <_init>:
 c58:	a9bf7bfd 	stp	x29, x30, [sp, #-16]!
 c5c:	910003fd 	mov	x29, sp
 c60:	94000038 	bl	d40 <call_weak_fn>
 c64:	a8c17bfd 	ldp	x29, x30, [sp], #16
 c68:	d65f03c0 	ret

Disassembly of section .plt:

0000000000000c70 <.plt>:
 c70:	a9bf7bf0 	stp	x16, x30, [sp, #-16]!
 c74:	f0000090 	adrp	x16, 13000 <__FRAME_END__+0xf4b0>
 c78:	f947fe11 	ldr	x17, [x16, #4088]
 c7c:	913fe210 	add	x16, x16, #0xff8
 c80:	d61f0220 	br	x17
 c84:	d503201f 	nop
 c88:	d503201f 	nop
 c8c:	d503201f 	nop

0000000000000c90 <memcpy@plt>:
 c90:	900000b0 	adrp	x16, 14000 <memcpy@GLIBC_2.17>
 c94:	f9400211 	ldr	x17, [x16]
 c98:	91000210 	add	x16, x16, #0x0
 c9c:	d61f0220 	br	x17

0000000000000ca0 <ImageProcess_ColorEnhace@plt>:
 ca0:	900000b0 	adrp	x16, 14000 <memcpy@GLIBC_2.17>
 ca4:	f9400611 	ldr	x17, [x16, #8]
 ca8:	91002210 	add	x16, x16, #0x8
 cac:	d61f0220 	br	x17

0000000000000cb0 <__cxa_finalize@plt>:
 cb0:	900000b0 	adrp	x16, 14000 <memcpy@GLIBC_2.17>
 cb4:	f9400a11 	ldr	x17, [x16, #16]
 cb8:	91004210 	add	x16, x16, #0x10
 cbc:	d61f0220 	br	x17

0000000000000cc0 <malloc@plt>:
 cc0:	900000b0 	adrp	x16, 14000 <memcpy@GLIBC_2.17>
 cc4:	f9400e11 	ldr	x17, [x16, #24]
 cc8:	91006210 	add	x16, x16, #0x18
 ccc:	d61f0220 	br	x17

0000000000000cd0 <fmodf@plt>:
 cd0:	900000b0 	adrp	x16, 14000 <memcpy@GLIBC_2.17>
 cd4:	f9401211 	ldr	x17, [x16, #32]
 cd8:	91008210 	add	x16, x16, #0x20
 cdc:	d61f0220 	br	x17

0000000000000ce0 <memset@plt>:
 ce0:	900000b0 	adrp	x16, 14000 <memcpy@GLIBC_2.17>
 ce4:	f9401611 	ldr	x17, [x16, #40]
 ce8:	9100a210 	add	x16, x16, #0x28
 cec:	d61f0220 	br	x17

0000000000000cf0 <fminf@plt>:
 cf0:	900000b0 	adrp	x16, 14000 <memcpy@GLIBC_2.17>
 cf4:	f9401a11 	ldr	x17, [x16, #48]
 cf8:	9100c210 	add	x16, x16, #0x30
 cfc:	d61f0220 	br	x17

0000000000000d00 <__gmon_start__@plt>:
 d00:	900000b0 	adrp	x16, 14000 <memcpy@GLIBC_2.17>
 d04:	f9401e11 	ldr	x17, [x16, #56]
 d08:	9100e210 	add	x16, x16, #0x38
 d0c:	d61f0220 	br	x17

0000000000000d10 <fmaxf@plt>:
 d10:	900000b0 	adrp	x16, 14000 <memcpy@GLIBC_2.17>
 d14:	f9402211 	ldr	x17, [x16, #64]
 d18:	91010210 	add	x16, x16, #0x40
 d1c:	d61f0220 	br	x17

0000000000000d20 <puts@plt>:
 d20:	900000b0 	adrp	x16, 14000 <memcpy@GLIBC_2.17>
 d24:	f9402611 	ldr	x17, [x16, #72]
 d28:	91012210 	add	x16, x16, #0x48
 d2c:	d61f0220 	br	x17

0000000000000d30 <free@plt>:
 d30:	900000b0 	adrp	x16, 14000 <memcpy@GLIBC_2.17>
 d34:	f9402a11 	ldr	x17, [x16, #80]
 d38:	91014210 	add	x16, x16, #0x50
 d3c:	d61f0220 	br	x17

Disassembly of section .text:

0000000000000d40 <call_weak_fn>:
     d40:	f0000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
     d44:	f947dc00 	ldr	x0, [x0, #4024]
     d48:	b4000040 	cbz	x0, d50 <call_weak_fn+0x10>
     d4c:	17ffffed 	b	d00 <__gmon_start__@plt>
     d50:	d65f03c0 	ret
     d54:	00000000 	.inst	0x00000000 ; undefined

0000000000000d58 <deregister_tm_clones>:
     d58:	900000a0 	adrp	x0, 14000 <memcpy@GLIBC_2.17>
     d5c:	91032000 	add	x0, x0, #0xc8
     d60:	900000a1 	adrp	x1, 14000 <memcpy@GLIBC_2.17>
     d64:	91032021 	add	x1, x1, #0xc8
     d68:	eb00003f 	cmp	x1, x0
     d6c:	540000a0 	b.eq	d80 <deregister_tm_clones+0x28>  // b.none
     d70:	f0000081 	adrp	x1, 13000 <__FRAME_END__+0xf4b0>
     d74:	f947cc21 	ldr	x1, [x1, #3992]
     d78:	b4000041 	cbz	x1, d80 <deregister_tm_clones+0x28>
     d7c:	d61f0020 	br	x1
     d80:	d65f03c0 	ret
     d84:	d503201f 	nop

0000000000000d88 <register_tm_clones>:
     d88:	900000a0 	adrp	x0, 14000 <memcpy@GLIBC_2.17>
     d8c:	91032000 	add	x0, x0, #0xc8
     d90:	900000a1 	adrp	x1, 14000 <memcpy@GLIBC_2.17>
     d94:	91032021 	add	x1, x1, #0xc8
     d98:	cb000021 	sub	x1, x1, x0
     d9c:	9343fc21 	asr	x1, x1, #3
     da0:	8b41fc21 	add	x1, x1, x1, lsr #63
     da4:	9341fc21 	asr	x1, x1, #1
     da8:	b40000a1 	cbz	x1, dbc <register_tm_clones+0x34>
     dac:	f0000082 	adrp	x2, 13000 <__FRAME_END__+0xf4b0>
     db0:	f947f042 	ldr	x2, [x2, #4064]
     db4:	b4000042 	cbz	x2, dbc <register_tm_clones+0x34>
     db8:	d61f0040 	br	x2
     dbc:	d65f03c0 	ret

0000000000000dc0 <__do_global_dtors_aux>:
     dc0:	a9be7bfd 	stp	x29, x30, [sp, #-32]!
     dc4:	910003fd 	mov	x29, sp
     dc8:	f9000bf3 	str	x19, [sp, #16]
     dcc:	900000b3 	adrp	x19, 14000 <memcpy@GLIBC_2.17>
     dd0:	39430a60 	ldrb	w0, [x19, #194]
     dd4:	35000140 	cbnz	w0, dfc <__do_global_dtors_aux+0x3c>
     dd8:	f0000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
     ddc:	f947d000 	ldr	x0, [x0, #4000]
     de0:	b4000080 	cbz	x0, df0 <__do_global_dtors_aux+0x30>
     de4:	900000a0 	adrp	x0, 14000 <memcpy@GLIBC_2.17>
     de8:	f9402c00 	ldr	x0, [x0, #88]
     dec:	97ffffb1 	bl	cb0 <__cxa_finalize@plt>
     df0:	97ffffda 	bl	d58 <deregister_tm_clones>
     df4:	52800020 	mov	w0, #0x1                   	// #1
     df8:	39030a60 	strb	w0, [x19, #194]
     dfc:	f9400bf3 	ldr	x19, [sp, #16]
     e00:	a8c27bfd 	ldp	x29, x30, [sp], #32
     e04:	d65f03c0 	ret

0000000000000e08 <frame_dummy>:
     e08:	17ffffe0 	b	d88 <register_tm_clones>

0000000000000e0c <SWAP_RGB_BGR>:
     e0c:	d100c3ff 	sub	sp, sp, #0x30
     e10:	f9000fe0 	str	x0, [sp, #24]
     e14:	f9000be1 	str	x1, [sp, #16]
     e18:	b9000fe2 	str	w2, [sp, #12]
     e1c:	b9002bff 	str	wzr, [sp, #40]
     e20:	b9002fff 	str	wzr, [sp, #44]
     e24:	14000023 	b	eb0 <SWAP_RGB_BGR+0xa4>
     e28:	b9802be0 	ldrsw	x0, [sp, #40]
     e2c:	91000800 	add	x0, x0, #0x2
     e30:	f9400fe1 	ldr	x1, [sp, #24]
     e34:	8b000021 	add	x1, x1, x0
     e38:	b9802be0 	ldrsw	x0, [sp, #40]
     e3c:	f9400be2 	ldr	x2, [sp, #16]
     e40:	8b000040 	add	x0, x2, x0
     e44:	39400021 	ldrb	w1, [x1]
     e48:	39000001 	strb	w1, [x0]
     e4c:	b9802be0 	ldrsw	x0, [sp, #40]
     e50:	91000400 	add	x0, x0, #0x1
     e54:	f9400fe1 	ldr	x1, [sp, #24]
     e58:	8b000021 	add	x1, x1, x0
     e5c:	b9802be0 	ldrsw	x0, [sp, #40]
     e60:	91000400 	add	x0, x0, #0x1
     e64:	f9400be2 	ldr	x2, [sp, #16]
     e68:	8b000040 	add	x0, x2, x0
     e6c:	39400021 	ldrb	w1, [x1]
     e70:	39000001 	strb	w1, [x0]
     e74:	b9802be0 	ldrsw	x0, [sp, #40]
     e78:	f9400fe1 	ldr	x1, [sp, #24]
     e7c:	8b000021 	add	x1, x1, x0
     e80:	b9802be0 	ldrsw	x0, [sp, #40]
     e84:	91000800 	add	x0, x0, #0x2
     e88:	f9400be2 	ldr	x2, [sp, #16]
     e8c:	8b000040 	add	x0, x2, x0
     e90:	39400021 	ldrb	w1, [x1]
     e94:	39000001 	strb	w1, [x0]
     e98:	b9402be0 	ldr	w0, [sp, #40]
     e9c:	11000c00 	add	w0, w0, #0x3
     ea0:	b9002be0 	str	w0, [sp, #40]
     ea4:	b9402fe0 	ldr	w0, [sp, #44]
     ea8:	11000400 	add	w0, w0, #0x1
     eac:	b9002fe0 	str	w0, [sp, #44]
     eb0:	b9402fe1 	ldr	w1, [sp, #44]
     eb4:	b9400fe0 	ldr	w0, [sp, #12]
     eb8:	6b00003f 	cmp	w1, w0
     ebc:	54fffb6b 	b.lt	e28 <SWAP_RGB_BGR+0x1c>  // b.tstop
     ec0:	d503201f 	nop
     ec4:	9100c3ff 	add	sp, sp, #0x30
     ec8:	d65f03c0 	ret

0000000000000ecc <bgr888_2_Y8>:
     ecc:	d100c3ff 	sub	sp, sp, #0x30
     ed0:	f9000fe0 	str	x0, [sp, #24]
     ed4:	f9000be1 	str	x1, [sp, #16]
     ed8:	b9000fe2 	str	w2, [sp, #12]
     edc:	b9000be3 	str	w3, [sp, #8]
     ee0:	b9002fff 	str	wzr, [sp, #44]
     ee4:	b9002bff 	str	wzr, [sp, #40]
     ee8:	1400002c 	b	f98 <bgr888_2_Y8+0xcc>
     eec:	b9802fe0 	ldrsw	x0, [sp, #44]
     ef0:	f9400fe1 	ldr	x1, [sp, #24]
     ef4:	8b000020 	add	x0, x1, x0
     ef8:	39400000 	ldrb	w0, [x0]
     efc:	2a0003e1 	mov	w1, w0
     f00:	52800e40 	mov	w0, #0x72                  	// #114
     f04:	1b007c21 	mul	w1, w1, w0
     f08:	b9802fe0 	ldrsw	x0, [sp, #44]
     f0c:	91000400 	add	x0, x0, #0x1
     f10:	f9400fe2 	ldr	x2, [sp, #24]
     f14:	8b000040 	add	x0, x2, x0
     f18:	39400000 	ldrb	w0, [x0]
     f1c:	2a0003e2 	mov	w2, w0
     f20:	52804960 	mov	w0, #0x24b                 	// #587
     f24:	1b007c40 	mul	w0, w2, w0
     f28:	0b000021 	add	w1, w1, w0
     f2c:	b9802fe0 	ldrsw	x0, [sp, #44]
     f30:	91000800 	add	x0, x0, #0x2
     f34:	f9400fe2 	ldr	x2, [sp, #24]
     f38:	8b000040 	add	x0, x2, x0
     f3c:	39400000 	ldrb	w0, [x0]
     f40:	2a0003e2 	mov	w2, w0
     f44:	52802560 	mov	w0, #0x12b                 	// #299
     f48:	1b007c40 	mul	w0, w2, w0
     f4c:	0b000020 	add	w0, w1, w0
     f50:	5289ba61 	mov	w1, #0x4dd3                	// #19923
     f54:	72a20c41 	movk	w1, #0x1062, lsl #16
     f58:	9b217c01 	smull	x1, w0, w1
     f5c:	d360fc21 	lsr	x1, x1, #32
     f60:	13067c21 	asr	w1, w1, #6
     f64:	131f7c00 	asr	w0, w0, #31
     f68:	4b000022 	sub	w2, w1, w0
     f6c:	b9802be0 	ldrsw	x0, [sp, #40]
     f70:	f9400be1 	ldr	x1, [sp, #16]
     f74:	8b000020 	add	x0, x1, x0
     f78:	12001c41 	and	w1, w2, #0xff
     f7c:	39000001 	strb	w1, [x0]
     f80:	b9402fe0 	ldr	w0, [sp, #44]
     f84:	11000c00 	add	w0, w0, #0x3
     f88:	b9002fe0 	str	w0, [sp, #44]
     f8c:	b9402be0 	ldr	w0, [sp, #40]
     f90:	11000400 	add	w0, w0, #0x1
     f94:	b9002be0 	str	w0, [sp, #40]
     f98:	b9400be1 	ldr	w1, [sp, #8]
     f9c:	b9400be0 	ldr	w0, [sp, #8]
     fa0:	1b007c20 	mul	w0, w1, w0
     fa4:	b9402be1 	ldr	w1, [sp, #40]
     fa8:	6b00003f 	cmp	w1, w0
     fac:	54fffa0b 	b.lt	eec <bgr888_2_Y8+0x20>  // b.tstop
     fb0:	d503201f 	nop
     fb4:	9100c3ff 	add	sp, sp, #0x30
     fb8:	d65f03c0 	ret

0000000000000fbc <rgb888_2_Y8>:
     fbc:	d100c3ff 	sub	sp, sp, #0x30
     fc0:	f9000fe0 	str	x0, [sp, #24]
     fc4:	f9000be1 	str	x1, [sp, #16]
     fc8:	b9000fe2 	str	w2, [sp, #12]
     fcc:	b9000be3 	str	w3, [sp, #8]
     fd0:	b9002fff 	str	wzr, [sp, #44]
     fd4:	b9002bff 	str	wzr, [sp, #40]
     fd8:	1400002c 	b	1088 <rgb888_2_Y8+0xcc>
     fdc:	b9802fe0 	ldrsw	x0, [sp, #44]
     fe0:	f9400fe1 	ldr	x1, [sp, #24]
     fe4:	8b000020 	add	x0, x1, x0
     fe8:	39400000 	ldrb	w0, [x0]
     fec:	2a0003e1 	mov	w1, w0
     ff0:	52802560 	mov	w0, #0x12b                 	// #299
     ff4:	1b007c21 	mul	w1, w1, w0
     ff8:	b9802fe0 	ldrsw	x0, [sp, #44]
     ffc:	91000400 	add	x0, x0, #0x1
    1000:	f9400fe2 	ldr	x2, [sp, #24]
    1004:	8b000040 	add	x0, x2, x0
    1008:	39400000 	ldrb	w0, [x0]
    100c:	2a0003e2 	mov	w2, w0
    1010:	52804960 	mov	w0, #0x24b                 	// #587
    1014:	1b007c40 	mul	w0, w2, w0
    1018:	0b000021 	add	w1, w1, w0
    101c:	b9802fe0 	ldrsw	x0, [sp, #44]
    1020:	91000800 	add	x0, x0, #0x2
    1024:	f9400fe2 	ldr	x2, [sp, #24]
    1028:	8b000040 	add	x0, x2, x0
    102c:	39400000 	ldrb	w0, [x0]
    1030:	2a0003e2 	mov	w2, w0
    1034:	52800e40 	mov	w0, #0x72                  	// #114
    1038:	1b007c40 	mul	w0, w2, w0
    103c:	0b000020 	add	w0, w1, w0
    1040:	5289ba61 	mov	w1, #0x4dd3                	// #19923
    1044:	72a20c41 	movk	w1, #0x1062, lsl #16
    1048:	9b217c01 	smull	x1, w0, w1
    104c:	d360fc21 	lsr	x1, x1, #32
    1050:	13067c21 	asr	w1, w1, #6
    1054:	131f7c00 	asr	w0, w0, #31
    1058:	4b000022 	sub	w2, w1, w0
    105c:	b9802be0 	ldrsw	x0, [sp, #40]
    1060:	f9400be1 	ldr	x1, [sp, #16]
    1064:	8b000020 	add	x0, x1, x0
    1068:	12001c41 	and	w1, w2, #0xff
    106c:	39000001 	strb	w1, [x0]
    1070:	b9402fe0 	ldr	w0, [sp, #44]
    1074:	11000c00 	add	w0, w0, #0x3
    1078:	b9002fe0 	str	w0, [sp, #44]
    107c:	b9402be0 	ldr	w0, [sp, #40]
    1080:	11000400 	add	w0, w0, #0x1
    1084:	b9002be0 	str	w0, [sp, #40]
    1088:	b9400be1 	ldr	w1, [sp, #8]
    108c:	b9400be0 	ldr	w0, [sp, #8]
    1090:	1b007c20 	mul	w0, w1, w0
    1094:	b9402be1 	ldr	w1, [sp, #40]
    1098:	6b00003f 	cmp	w1, w0
    109c:	54fffa0b 	b.lt	fdc <rgb888_2_Y8+0x20>  // b.tstop
    10a0:	d503201f 	nop
    10a4:	9100c3ff 	add	sp, sp, #0x30
    10a8:	d65f03c0 	ret

00000000000010ac <Y8ToY4>:
    10ac:	d100c3ff 	sub	sp, sp, #0x30
    10b0:	f9000fe0 	str	x0, [sp, #24]
    10b4:	f9000be1 	str	x1, [sp, #16]
    10b8:	b9000fe2 	str	w2, [sp, #12]
    10bc:	b9000be3 	str	w3, [sp, #8]
    10c0:	b9002fff 	str	wzr, [sp, #44]
    10c4:	b9002fff 	str	wzr, [sp, #44]
    10c8:	1400002c 	b	1178 <Y8ToY4+0xcc>
    10cc:	b9402fe0 	ldr	w0, [sp, #44]
    10d0:	12000000 	and	w0, w0, #0x1
    10d4:	7100001f 	cmp	w0, #0x0
    10d8:	54000201 	b.ne	1118 <Y8ToY4+0x6c>  // b.any
    10dc:	b9802fe0 	ldrsw	x0, [sp, #44]
    10e0:	f9400fe1 	ldr	x1, [sp, #24]
    10e4:	8b000020 	add	x0, x1, x0
    10e8:	39400001 	ldrb	w1, [x0]
    10ec:	b9402fe0 	ldr	w0, [sp, #44]
    10f0:	531f7c02 	lsr	w2, w0, #31
    10f4:	0b000040 	add	w0, w2, w0
    10f8:	13017c00 	asr	w0, w0, #1
    10fc:	93407c00 	sxtw	x0, w0
    1100:	f9400be2 	ldr	x2, [sp, #16]
    1104:	8b000040 	add	x0, x2, x0
    1108:	53047c21 	lsr	w1, w1, #4
    110c:	12001c21 	and	w1, w1, #0xff
    1110:	39000001 	strb	w1, [x0]
    1114:	14000016 	b	116c <Y8ToY4+0xc0>
    1118:	b9402fe0 	ldr	w0, [sp, #44]
    111c:	531f7c01 	lsr	w1, w0, #31
    1120:	0b000020 	add	w0, w1, w0
    1124:	13017c00 	asr	w0, w0, #1
    1128:	2a0003e3 	mov	w3, w0
    112c:	93407c60 	sxtw	x0, w3
    1130:	f9400be1 	ldr	x1, [sp, #16]
    1134:	8b000020 	add	x0, x1, x0
    1138:	39400002 	ldrb	w2, [x0]
    113c:	b9802fe0 	ldrsw	x0, [sp, #44]
    1140:	f9400fe1 	ldr	x1, [sp, #24]
    1144:	8b000020 	add	x0, x1, x0
    1148:	39400000 	ldrb	w0, [x0]
    114c:	121c6c00 	and	w0, w0, #0xfffffff0
    1150:	12001c01 	and	w1, w0, #0xff
    1154:	93407c60 	sxtw	x0, w3
    1158:	f9400be3 	ldr	x3, [sp, #16]
    115c:	8b000060 	add	x0, x3, x0
    1160:	0b010041 	add	w1, w2, w1
    1164:	12001c21 	and	w1, w1, #0xff
    1168:	39000001 	strb	w1, [x0]
    116c:	b9402fe0 	ldr	w0, [sp, #44]
    1170:	11000400 	add	w0, w0, #0x1
    1174:	b9002fe0 	str	w0, [sp, #44]
    1178:	b9400fe1 	ldr	w1, [sp, #12]
    117c:	b9400be0 	ldr	w0, [sp, #8]
    1180:	1b007c20 	mul	w0, w1, w0
    1184:	b9402fe1 	ldr	w1, [sp, #44]
    1188:	6b00003f 	cmp	w1, w0
    118c:	54fffa0b 	b.lt	10cc <Y8ToY4+0x20>  // b.tstop
    1190:	d503201f 	nop
    1194:	9100c3ff 	add	sp, sp, #0x30
    1198:	d65f03c0 	ret

000000000000119c <bgr888_2_Y1>:
    119c:	d10103ff 	sub	sp, sp, #0x40
    11a0:	f9000fe0 	str	x0, [sp, #24]
    11a4:	f9000be1 	str	x1, [sp, #16]
    11a8:	b9000fe2 	str	w2, [sp, #12]
    11ac:	b9000be3 	str	w3, [sp, #8]
    11b0:	b9003fff 	str	wzr, [sp, #60]
    11b4:	b9003bff 	str	wzr, [sp, #56]
    11b8:	b90037ff 	str	wzr, [sp, #52]
    11bc:	b90033ff 	str	wzr, [sp, #48]
    11c0:	3900bfff 	strb	wzr, [sp, #47]
    11c4:	b9003fff 	str	wzr, [sp, #60]
    11c8:	14000041 	b	12cc <bgr888_2_Y1+0x130>
    11cc:	b9803be0 	ldrsw	x0, [sp, #56]
    11d0:	f9400fe1 	ldr	x1, [sp, #24]
    11d4:	8b000020 	add	x0, x1, x0
    11d8:	39400000 	ldrb	w0, [x0]
    11dc:	1e620001 	scvtf	d1, w0
    11e0:	d0000000 	adrp	x0, 3000 <ImageProcess_Dither_T2001+0x70>
    11e4:	912a4000 	add	x0, x0, #0xa90
    11e8:	fd400000 	ldr	d0, [x0]
    11ec:	1e600821 	fmul	d1, d1, d0
    11f0:	b9803be0 	ldrsw	x0, [sp, #56]
    11f4:	91000400 	add	x0, x0, #0x1
    11f8:	f9400fe1 	ldr	x1, [sp, #24]
    11fc:	8b000020 	add	x0, x1, x0
    1200:	39400000 	ldrb	w0, [x0]
    1204:	1e620002 	scvtf	d2, w0
    1208:	d0000000 	adrp	x0, 3000 <ImageProcess_Dither_T2001+0x70>
    120c:	912a6000 	add	x0, x0, #0xa98
    1210:	fd400000 	ldr	d0, [x0]
    1214:	1e600840 	fmul	d0, d2, d0
    1218:	1e602821 	fadd	d1, d1, d0
    121c:	b9803be0 	ldrsw	x0, [sp, #56]
    1220:	91000800 	add	x0, x0, #0x2
    1224:	f9400fe1 	ldr	x1, [sp, #24]
    1228:	8b000020 	add	x0, x1, x0
    122c:	39400000 	ldrb	w0, [x0]
    1230:	1e620002 	scvtf	d2, w0
    1234:	d0000000 	adrp	x0, 3000 <ImageProcess_Dither_T2001+0x70>
    1238:	912a8000 	add	x0, x0, #0xaa0
    123c:	fd400000 	ldr	d0, [x0]
    1240:	1e600840 	fmul	d0, d2, d0
    1244:	1e602820 	fadd	d0, d1, d0
    1248:	1e790000 	fcvtzu	w0, d0
    124c:	3900bbe0 	strb	w0, [sp, #46]
    1250:	b9403be0 	ldr	w0, [sp, #56]
    1254:	11000c00 	add	w0, w0, #0x3
    1258:	b9003be0 	str	w0, [sp, #56]
    125c:	3940bfe0 	ldrb	w0, [sp, #47]
    1260:	53017c00 	lsr	w0, w0, #1
    1264:	3900bfe0 	strb	w0, [sp, #47]
    1268:	3940bbe0 	ldrb	w0, [sp, #46]
    126c:	7102001f 	cmp	w0, #0x80
    1270:	54000089 	b.ls	1280 <bgr888_2_Y1+0xe4>  // b.plast
    1274:	3940bfe0 	ldrb	w0, [sp, #47]
    1278:	32196000 	orr	w0, w0, #0xffffff80
    127c:	3900bfe0 	strb	w0, [sp, #47]
    1280:	b94033e0 	ldr	w0, [sp, #48]
    1284:	11000400 	add	w0, w0, #0x1
    1288:	b90033e0 	str	w0, [sp, #48]
    128c:	b94033e0 	ldr	w0, [sp, #48]
    1290:	7100201f 	cmp	w0, #0x8
    1294:	54000161 	b.ne	12c0 <bgr888_2_Y1+0x124>  // b.any
    1298:	b94037e0 	ldr	w0, [sp, #52]
    129c:	11000401 	add	w1, w0, #0x1
    12a0:	b90037e1 	str	w1, [sp, #52]
    12a4:	93407c00 	sxtw	x0, w0
    12a8:	f9400be1 	ldr	x1, [sp, #16]
    12ac:	8b000020 	add	x0, x1, x0
    12b0:	3940bfe1 	ldrb	w1, [sp, #47]
    12b4:	39000001 	strb	w1, [x0]
    12b8:	3900bfff 	strb	wzr, [sp, #47]
    12bc:	b90033ff 	str	wzr, [sp, #48]
    12c0:	b9403fe0 	ldr	w0, [sp, #60]
    12c4:	11000400 	add	w0, w0, #0x1
    12c8:	b9003fe0 	str	w0, [sp, #60]
    12cc:	b9400fe1 	ldr	w1, [sp, #12]
    12d0:	b9400be0 	ldr	w0, [sp, #8]
    12d4:	1b007c20 	mul	w0, w1, w0
    12d8:	b9403fe1 	ldr	w1, [sp, #60]
    12dc:	6b00003f 	cmp	w1, w0
    12e0:	54fff76b 	b.lt	11cc <bgr888_2_Y1+0x30>  // b.tstop
    12e4:	d503201f 	nop
    12e8:	910103ff 	add	sp, sp, #0x40
    12ec:	d65f03c0 	ret

00000000000012f0 <rgb888_2_Y1>:
    12f0:	d10103ff 	sub	sp, sp, #0x40
    12f4:	f9000fe0 	str	x0, [sp, #24]
    12f8:	f9000be1 	str	x1, [sp, #16]
    12fc:	b9000fe2 	str	w2, [sp, #12]
    1300:	b9000be3 	str	w3, [sp, #8]
    1304:	b9003fff 	str	wzr, [sp, #60]
    1308:	b9003bff 	str	wzr, [sp, #56]
    130c:	b90037ff 	str	wzr, [sp, #52]
    1310:	b90033ff 	str	wzr, [sp, #48]
    1314:	3900bfff 	strb	wzr, [sp, #47]
    1318:	b9003fff 	str	wzr, [sp, #60]
    131c:	14000041 	b	1420 <rgb888_2_Y1+0x130>
    1320:	b9803be0 	ldrsw	x0, [sp, #56]
    1324:	f9400fe1 	ldr	x1, [sp, #24]
    1328:	8b000020 	add	x0, x1, x0
    132c:	39400000 	ldrb	w0, [x0]
    1330:	1e620001 	scvtf	d1, w0
    1334:	d0000000 	adrp	x0, 3000 <ImageProcess_Dither_T2001+0x70>
    1338:	912aa000 	add	x0, x0, #0xaa8
    133c:	fd400000 	ldr	d0, [x0]
    1340:	1e600821 	fmul	d1, d1, d0
    1344:	b9803be0 	ldrsw	x0, [sp, #56]
    1348:	91000400 	add	x0, x0, #0x1
    134c:	f9400fe1 	ldr	x1, [sp, #24]
    1350:	8b000020 	add	x0, x1, x0
    1354:	39400000 	ldrb	w0, [x0]
    1358:	1e620002 	scvtf	d2, w0
    135c:	d0000000 	adrp	x0, 3000 <ImageProcess_Dither_T2001+0x70>
    1360:	912ac000 	add	x0, x0, #0xab0
    1364:	fd400000 	ldr	d0, [x0]
    1368:	1e600840 	fmul	d0, d2, d0
    136c:	1e602821 	fadd	d1, d1, d0
    1370:	b9803be0 	ldrsw	x0, [sp, #56]
    1374:	91000800 	add	x0, x0, #0x2
    1378:	f9400fe1 	ldr	x1, [sp, #24]
    137c:	8b000020 	add	x0, x1, x0
    1380:	39400000 	ldrb	w0, [x0]
    1384:	1e620002 	scvtf	d2, w0
    1388:	d0000000 	adrp	x0, 3000 <ImageProcess_Dither_T2001+0x70>
    138c:	912ae000 	add	x0, x0, #0xab8
    1390:	fd400000 	ldr	d0, [x0]
    1394:	1e600840 	fmul	d0, d2, d0
    1398:	1e602820 	fadd	d0, d1, d0
    139c:	1e790000 	fcvtzu	w0, d0
    13a0:	3900bbe0 	strb	w0, [sp, #46]
    13a4:	b9403be0 	ldr	w0, [sp, #56]
    13a8:	11000c00 	add	w0, w0, #0x3
    13ac:	b9003be0 	str	w0, [sp, #56]
    13b0:	3940bfe0 	ldrb	w0, [sp, #47]
    13b4:	53017c00 	lsr	w0, w0, #1
    13b8:	3900bfe0 	strb	w0, [sp, #47]
    13bc:	3940bbe0 	ldrb	w0, [sp, #46]
    13c0:	7102001f 	cmp	w0, #0x80
    13c4:	54000089 	b.ls	13d4 <rgb888_2_Y1+0xe4>  // b.plast
    13c8:	3940bfe0 	ldrb	w0, [sp, #47]
    13cc:	32196000 	orr	w0, w0, #0xffffff80
    13d0:	3900bfe0 	strb	w0, [sp, #47]
    13d4:	b94033e0 	ldr	w0, [sp, #48]
    13d8:	11000400 	add	w0, w0, #0x1
    13dc:	b90033e0 	str	w0, [sp, #48]
    13e0:	b94033e0 	ldr	w0, [sp, #48]
    13e4:	7100201f 	cmp	w0, #0x8
    13e8:	54000161 	b.ne	1414 <rgb888_2_Y1+0x124>  // b.any
    13ec:	b94037e0 	ldr	w0, [sp, #52]
    13f0:	11000401 	add	w1, w0, #0x1
    13f4:	b90037e1 	str	w1, [sp, #52]
    13f8:	93407c00 	sxtw	x0, w0
    13fc:	f9400be1 	ldr	x1, [sp, #16]
    1400:	8b000020 	add	x0, x1, x0
    1404:	3940bfe1 	ldrb	w1, [sp, #47]
    1408:	39000001 	strb	w1, [x0]
    140c:	3900bfff 	strb	wzr, [sp, #47]
    1410:	b90033ff 	str	wzr, [sp, #48]
    1414:	b9403fe0 	ldr	w0, [sp, #60]
    1418:	11000400 	add	w0, w0, #0x1
    141c:	b9003fe0 	str	w0, [sp, #60]
    1420:	b9400fe1 	ldr	w1, [sp, #12]
    1424:	b9400be0 	ldr	w0, [sp, #8]
    1428:	1b007c20 	mul	w0, w1, w0
    142c:	b9403fe1 	ldr	w1, [sp, #60]
    1430:	6b00003f 	cmp	w1, w0
    1434:	54fff76b 	b.lt	1320 <rgb888_2_Y1+0x30>  // b.tstop
    1438:	d503201f 	nop
    143c:	910103ff 	add	sp, sp, #0x40
    1440:	d65f03c0 	ret

0000000000001444 <kaleido_s1_transfer_ec253tt1>:
    1444:	a9b77bfd 	stp	x29, x30, [sp, #-144]!
    1448:	910003fd 	mov	x29, sp
    144c:	f90017a0 	str	x0, [x29, #40]
    1450:	f90013a1 	str	x1, [x29, #32]
    1454:	b9001fa2 	str	w2, [x29, #28]
    1458:	b9001ba3 	str	w3, [x29, #24]
    145c:	b90017a4 	str	w4, [x29, #20]
    1460:	b90013a5 	str	w5, [x29, #16]
    1464:	1e201000 	fmov	s0, #2.000000000000000000e+00
    1468:	bd006fa0 	str	s0, [x29, #108]
    146c:	b9008fbf 	str	wzr, [x29, #140]
    1470:	1400013c 	b	1960 <kaleido_s1_transfer_ec253tt1+0x51c>
    1474:	b9808fa0 	ldrsw	x0, [x29, #140]
    1478:	f94017a1 	ldr	x1, [x29, #40]
    147c:	8b000020 	add	x0, x1, x0
    1480:	39400000 	ldrb	w0, [x0]
    1484:	1e220000 	scvtf	s0, w0
    1488:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    148c:	1e270001 	fmov	s1, w0
    1490:	1e211800 	fdiv	s0, s0, s1
    1494:	bd0063a0 	str	s0, [x29, #96]
    1498:	b9808fa0 	ldrsw	x0, [x29, #140]
    149c:	91000400 	add	x0, x0, #0x1
    14a0:	f94017a1 	ldr	x1, [x29, #40]
    14a4:	8b000020 	add	x0, x1, x0
    14a8:	39400000 	ldrb	w0, [x0]
    14ac:	1e220000 	scvtf	s0, w0
    14b0:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    14b4:	1e270001 	fmov	s1, w0
    14b8:	1e211800 	fdiv	s0, s0, s1
    14bc:	bd005fa0 	str	s0, [x29, #92]
    14c0:	b9808fa0 	ldrsw	x0, [x29, #140]
    14c4:	91000800 	add	x0, x0, #0x2
    14c8:	f94017a1 	ldr	x1, [x29, #40]
    14cc:	8b000020 	add	x0, x1, x0
    14d0:	39400000 	ldrb	w0, [x0]
    14d4:	1e220000 	scvtf	s0, w0
    14d8:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    14dc:	1e270001 	fmov	s1, w0
    14e0:	1e211800 	fdiv	s0, s0, s1
    14e4:	bd005ba0 	str	s0, [x29, #88]
    14e8:	bd4063a1 	ldr	s1, [x29, #96]
    14ec:	bd405fa0 	ldr	s0, [x29, #92]
    14f0:	97fffe08 	bl	d10 <fmaxf@plt>
    14f4:	1e204001 	fmov	s1, s0
    14f8:	bd405ba0 	ldr	s0, [x29, #88]
    14fc:	97fffe05 	bl	d10 <fmaxf@plt>
    1500:	bd0057a0 	str	s0, [x29, #84]
    1504:	bd4063a1 	ldr	s1, [x29, #96]
    1508:	bd405fa0 	ldr	s0, [x29, #92]
    150c:	97fffdf9 	bl	cf0 <fminf@plt>
    1510:	1e204001 	fmov	s1, s0
    1514:	bd405ba0 	ldr	s0, [x29, #88]
    1518:	97fffdf6 	bl	cf0 <fminf@plt>
    151c:	bd0053a0 	str	s0, [x29, #80]
    1520:	bd4057a1 	ldr	s1, [x29, #84]
    1524:	bd4053a0 	ldr	s0, [x29, #80]
    1528:	1e203820 	fsub	s0, s1, s0
    152c:	bd004fa0 	str	s0, [x29, #76]
    1530:	b9008bbf 	str	wzr, [x29, #136]
    1534:	bd4057a0 	ldr	s0, [x29, #84]
    1538:	bd004ba0 	str	s0, [x29, #72]
    153c:	bd404fa0 	ldr	s0, [x29, #76]
    1540:	5296e2e0 	mov	w0, #0xb717                	// #46871
    1544:	72a71a20 	movk	w0, #0x38d1, lsl #16
    1548:	1e270001 	fmov	s1, w0
    154c:	1e212010 	fcmpe	s0, s1
    1550:	5400074d 	b.le	1638 <kaleido_s1_transfer_ec253tt1+0x1f4>
    1554:	bd4057a0 	ldr	s0, [x29, #84]
    1558:	bd404fa1 	ldr	s1, [x29, #76]
    155c:	1e201820 	fdiv	s0, s1, s0
    1560:	bd0087a0 	str	s0, [x29, #132]
    1564:	bd4057a1 	ldr	s1, [x29, #84]
    1568:	bd405ba0 	ldr	s0, [x29, #88]
    156c:	1e202020 	fcmp	s1, s0
    1570:	540001c1 	b.ne	15a8 <kaleido_s1_transfer_ec253tt1+0x164>  // b.any
    1574:	bd405fa1 	ldr	s1, [x29, #92]
    1578:	bd4063a0 	ldr	s0, [x29, #96]
    157c:	1e203821 	fsub	s1, s1, s0
    1580:	bd404fa0 	ldr	s0, [x29, #76]
    1584:	1e201820 	fdiv	s0, s1, s0
    1588:	1e231001 	fmov	s1, #6.000000000000000000e+00
    158c:	97fffdd1 	bl	cd0 <fmodf@plt>
    1590:	1e204001 	fmov	s1, s0
    1594:	52a84e00 	mov	w0, #0x42700000            	// #1114636288
    1598:	1e270000 	fmov	s0, w0
    159c:	1e200820 	fmul	s0, s1, s0
    15a0:	bd008ba0 	str	s0, [x29, #136]
    15a4:	1400001c 	b	1614 <kaleido_s1_transfer_ec253tt1+0x1d0>
    15a8:	bd4057a1 	ldr	s1, [x29, #84]
    15ac:	bd405fa0 	ldr	s0, [x29, #92]
    15b0:	1e202020 	fcmp	s1, s0
    15b4:	540001a1 	b.ne	15e8 <kaleido_s1_transfer_ec253tt1+0x1a4>  // b.any
    15b8:	bd4063a1 	ldr	s1, [x29, #96]
    15bc:	bd405ba0 	ldr	s0, [x29, #88]
    15c0:	1e203821 	fsub	s1, s1, s0
    15c4:	bd404fa0 	ldr	s0, [x29, #76]
    15c8:	1e201821 	fdiv	s1, s1, s0
    15cc:	1e201000 	fmov	s0, #2.000000000000000000e+00
    15d0:	1e202820 	fadd	s0, s1, s0
    15d4:	52a84e00 	mov	w0, #0x42700000            	// #1114636288
    15d8:	1e270001 	fmov	s1, w0
    15dc:	1e210800 	fmul	s0, s0, s1
    15e0:	bd008ba0 	str	s0, [x29, #136]
    15e4:	1400000c 	b	1614 <kaleido_s1_transfer_ec253tt1+0x1d0>
    15e8:	bd405ba1 	ldr	s1, [x29, #88]
    15ec:	bd405fa0 	ldr	s0, [x29, #92]
    15f0:	1e203821 	fsub	s1, s1, s0
    15f4:	bd404fa0 	ldr	s0, [x29, #76]
    15f8:	1e201821 	fdiv	s1, s1, s0
    15fc:	1e221000 	fmov	s0, #4.000000000000000000e+00
    1600:	1e202820 	fadd	s0, s1, s0
    1604:	52a84e00 	mov	w0, #0x42700000            	// #1114636288
    1608:	1e270001 	fmov	s1, w0
    160c:	1e210800 	fmul	s0, s0, s1
    1610:	bd008ba0 	str	s0, [x29, #136]
    1614:	bd408ba0 	ldr	s0, [x29, #136]
    1618:	1e202018 	fcmpe	s0, #0.0
    161c:	54000125 	b.pl	1640 <kaleido_s1_transfer_ec253tt1+0x1fc>  // b.nfrst
    1620:	bd408ba0 	ldr	s0, [x29, #136]
    1624:	52a87680 	mov	w0, #0x43b40000            	// #1135869952
    1628:	1e270001 	fmov	s1, w0
    162c:	1e212800 	fadd	s0, s0, s1
    1630:	bd008ba0 	str	s0, [x29, #136]
    1634:	14000003 	b	1640 <kaleido_s1_transfer_ec253tt1+0x1fc>
    1638:	b90087bf 	str	wzr, [x29, #132]
    163c:	b9008bbf 	str	wzr, [x29, #136]
    1640:	bd4087a1 	ldr	s1, [x29, #132]
    1644:	bd406fa0 	ldr	s0, [x29, #108]
    1648:	1e200820 	fmul	s0, s1, s0
    164c:	bd0087a0 	str	s0, [x29, #132]
    1650:	0f000401 	movi	v1.2s, #0x0
    1654:	bd4087a0 	ldr	s0, [x29, #132]
    1658:	97fffdae 	bl	d10 <fmaxf@plt>
    165c:	1e2e1001 	fmov	s1, #1.000000000000000000e+00
    1660:	97fffda4 	bl	cf0 <fminf@plt>
    1664:	bd0087a0 	str	s0, [x29, #132]
    1668:	bd404ba1 	ldr	s1, [x29, #72]
    166c:	bd4087a0 	ldr	s0, [x29, #132]
    1670:	1e200820 	fmul	s0, s1, s0
    1674:	bd0047a0 	str	s0, [x29, #68]
    1678:	52a84e00 	mov	w0, #0x42700000            	// #1114636288
    167c:	1e270001 	fmov	s1, w0
    1680:	bd408ba0 	ldr	s0, [x29, #136]
    1684:	1e211800 	fdiv	s0, s0, s1
    1688:	1e201001 	fmov	s1, #2.000000000000000000e+00
    168c:	97fffd91 	bl	cd0 <fmodf@plt>
    1690:	1e204001 	fmov	s1, s0
    1694:	1e2e1000 	fmov	s0, #1.000000000000000000e+00
    1698:	1e203820 	fsub	s0, s1, s0
    169c:	1e20c000 	fabs	s0, s0
    16a0:	1e2e1001 	fmov	s1, #1.000000000000000000e+00
    16a4:	1e203820 	fsub	s0, s1, s0
    16a8:	bd4047a1 	ldr	s1, [x29, #68]
    16ac:	1e200820 	fmul	s0, s1, s0
    16b0:	bd0043a0 	str	s0, [x29, #64]
    16b4:	bd404ba1 	ldr	s1, [x29, #72]
    16b8:	bd4047a0 	ldr	s0, [x29, #68]
    16bc:	1e203820 	fsub	s0, s1, s0
    16c0:	bd003fa0 	str	s0, [x29, #60]
    16c4:	bd408ba0 	ldr	s0, [x29, #136]
    16c8:	52a84e00 	mov	w0, #0x42700000            	// #1114636288
    16cc:	1e270001 	fmov	s1, w0
    16d0:	1e212010 	fcmpe	s0, s1
    16d4:	540000e5 	b.pl	16f0 <kaleido_s1_transfer_ec253tt1+0x2ac>  // b.nfrst
    16d8:	bd4047a0 	ldr	s0, [x29, #68]
    16dc:	bd0083a0 	str	s0, [x29, #128]
    16e0:	bd4043a0 	ldr	s0, [x29, #64]
    16e4:	bd007fa0 	str	s0, [x29, #124]
    16e8:	b9007bbf 	str	wzr, [x29, #120]
    16ec:	14000032 	b	17b4 <kaleido_s1_transfer_ec253tt1+0x370>
    16f0:	bd408ba0 	ldr	s0, [x29, #136]
    16f4:	52a85e00 	mov	w0, #0x42f00000            	// #1123024896
    16f8:	1e270001 	fmov	s1, w0
    16fc:	1e212010 	fcmpe	s0, s1
    1700:	540000e5 	b.pl	171c <kaleido_s1_transfer_ec253tt1+0x2d8>  // b.nfrst
    1704:	bd4043a0 	ldr	s0, [x29, #64]
    1708:	bd0083a0 	str	s0, [x29, #128]
    170c:	bd4047a0 	ldr	s0, [x29, #68]
    1710:	bd007fa0 	str	s0, [x29, #124]
    1714:	b9007bbf 	str	wzr, [x29, #120]
    1718:	14000027 	b	17b4 <kaleido_s1_transfer_ec253tt1+0x370>
    171c:	bd408ba0 	ldr	s0, [x29, #136]
    1720:	52a86680 	mov	w0, #0x43340000            	// #1127481344
    1724:	1e270001 	fmov	s1, w0
    1728:	1e212010 	fcmpe	s0, s1
    172c:	540000e5 	b.pl	1748 <kaleido_s1_transfer_ec253tt1+0x304>  // b.nfrst
    1730:	b90083bf 	str	wzr, [x29, #128]
    1734:	bd4047a0 	ldr	s0, [x29, #68]
    1738:	bd007fa0 	str	s0, [x29, #124]
    173c:	bd4043a0 	ldr	s0, [x29, #64]
    1740:	bd007ba0 	str	s0, [x29, #120]
    1744:	1400001c 	b	17b4 <kaleido_s1_transfer_ec253tt1+0x370>
    1748:	bd408ba0 	ldr	s0, [x29, #136]
    174c:	52a86e00 	mov	w0, #0x43700000            	// #1131413504
    1750:	1e270001 	fmov	s1, w0
    1754:	1e212010 	fcmpe	s0, s1
    1758:	540000e5 	b.pl	1774 <kaleido_s1_transfer_ec253tt1+0x330>  // b.nfrst
    175c:	b90083bf 	str	wzr, [x29, #128]
    1760:	bd4043a0 	ldr	s0, [x29, #64]
    1764:	bd007fa0 	str	s0, [x29, #124]
    1768:	bd4047a0 	ldr	s0, [x29, #68]
    176c:	bd007ba0 	str	s0, [x29, #120]
    1770:	14000011 	b	17b4 <kaleido_s1_transfer_ec253tt1+0x370>
    1774:	bd408ba0 	ldr	s0, [x29, #136]
    1778:	52a872c0 	mov	w0, #0x43960000            	// #1133903872
    177c:	1e270001 	fmov	s1, w0
    1780:	1e212010 	fcmpe	s0, s1
    1784:	540000e5 	b.pl	17a0 <kaleido_s1_transfer_ec253tt1+0x35c>  // b.nfrst
    1788:	bd4043a0 	ldr	s0, [x29, #64]
    178c:	bd0083a0 	str	s0, [x29, #128]
    1790:	b9007fbf 	str	wzr, [x29, #124]
    1794:	bd4047a0 	ldr	s0, [x29, #68]
    1798:	bd007ba0 	str	s0, [x29, #120]
    179c:	14000006 	b	17b4 <kaleido_s1_transfer_ec253tt1+0x370>
    17a0:	bd4047a0 	ldr	s0, [x29, #68]
    17a4:	bd0083a0 	str	s0, [x29, #128]
    17a8:	b9007fbf 	str	wzr, [x29, #124]
    17ac:	bd4043a0 	ldr	s0, [x29, #64]
    17b0:	bd007ba0 	str	s0, [x29, #120]
    17b4:	bd407ba1 	ldr	s1, [x29, #120]
    17b8:	bd403fa0 	ldr	s0, [x29, #60]
    17bc:	1e202820 	fadd	s0, s1, s0
    17c0:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    17c4:	1e270001 	fmov	s1, w0
    17c8:	1e210800 	fmul	s0, s0, s1
    17cc:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    17d0:	1e270001 	fmov	s1, w0
    17d4:	1e212010 	fcmpe	s0, s1
    17d8:	5400006d 	b.le	17e4 <kaleido_s1_transfer_ec253tt1+0x3a0>
    17dc:	52801fe0 	mov	w0, #0xff                  	// #255
    17e0:	14000013 	b	182c <kaleido_s1_transfer_ec253tt1+0x3e8>
    17e4:	bd407ba1 	ldr	s1, [x29, #120]
    17e8:	bd403fa0 	ldr	s0, [x29, #60]
    17ec:	1e202820 	fadd	s0, s1, s0
    17f0:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    17f4:	1e270001 	fmov	s1, w0
    17f8:	1e210800 	fmul	s0, s0, s1
    17fc:	1e202018 	fcmpe	s0, #0.0
    1800:	54000065 	b.pl	180c <kaleido_s1_transfer_ec253tt1+0x3c8>  // b.nfrst
    1804:	52800000 	mov	w0, #0x0                   	// #0
    1808:	14000009 	b	182c <kaleido_s1_transfer_ec253tt1+0x3e8>
    180c:	bd407ba1 	ldr	s1, [x29, #120]
    1810:	bd403fa0 	ldr	s0, [x29, #60]
    1814:	1e202820 	fadd	s0, s1, s0
    1818:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    181c:	1e270001 	fmov	s1, w0
    1820:	1e210800 	fmul	s0, s0, s1
    1824:	1e390000 	fcvtzu	w0, s0
    1828:	12001c00 	and	w0, w0, #0xff
    182c:	b9808fa1 	ldrsw	x1, [x29, #140]
    1830:	f94017a2 	ldr	x2, [x29, #40]
    1834:	8b010041 	add	x1, x2, x1
    1838:	39000020 	strb	w0, [x1]
    183c:	bd407fa1 	ldr	s1, [x29, #124]
    1840:	bd403fa0 	ldr	s0, [x29, #60]
    1844:	1e202820 	fadd	s0, s1, s0
    1848:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    184c:	1e270001 	fmov	s1, w0
    1850:	1e210800 	fmul	s0, s0, s1
    1854:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1858:	1e270001 	fmov	s1, w0
    185c:	1e212010 	fcmpe	s0, s1
    1860:	5400006d 	b.le	186c <kaleido_s1_transfer_ec253tt1+0x428>
    1864:	52801fe0 	mov	w0, #0xff                  	// #255
    1868:	14000013 	b	18b4 <kaleido_s1_transfer_ec253tt1+0x470>
    186c:	bd407fa1 	ldr	s1, [x29, #124]
    1870:	bd403fa0 	ldr	s0, [x29, #60]
    1874:	1e202820 	fadd	s0, s1, s0
    1878:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    187c:	1e270001 	fmov	s1, w0
    1880:	1e210800 	fmul	s0, s0, s1
    1884:	1e202018 	fcmpe	s0, #0.0
    1888:	54000065 	b.pl	1894 <kaleido_s1_transfer_ec253tt1+0x450>  // b.nfrst
    188c:	52800000 	mov	w0, #0x0                   	// #0
    1890:	14000009 	b	18b4 <kaleido_s1_transfer_ec253tt1+0x470>
    1894:	bd407fa1 	ldr	s1, [x29, #124]
    1898:	bd403fa0 	ldr	s0, [x29, #60]
    189c:	1e202820 	fadd	s0, s1, s0
    18a0:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    18a4:	1e270001 	fmov	s1, w0
    18a8:	1e210800 	fmul	s0, s0, s1
    18ac:	1e390000 	fcvtzu	w0, s0
    18b0:	12001c00 	and	w0, w0, #0xff
    18b4:	b9808fa1 	ldrsw	x1, [x29, #140]
    18b8:	91000421 	add	x1, x1, #0x1
    18bc:	f94017a2 	ldr	x2, [x29, #40]
    18c0:	8b010041 	add	x1, x2, x1
    18c4:	39000020 	strb	w0, [x1]
    18c8:	bd4083a1 	ldr	s1, [x29, #128]
    18cc:	bd403fa0 	ldr	s0, [x29, #60]
    18d0:	1e202820 	fadd	s0, s1, s0
    18d4:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    18d8:	1e270001 	fmov	s1, w0
    18dc:	1e210800 	fmul	s0, s0, s1
    18e0:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    18e4:	1e270001 	fmov	s1, w0
    18e8:	1e212010 	fcmpe	s0, s1
    18ec:	5400006d 	b.le	18f8 <kaleido_s1_transfer_ec253tt1+0x4b4>
    18f0:	52801fe0 	mov	w0, #0xff                  	// #255
    18f4:	14000013 	b	1940 <kaleido_s1_transfer_ec253tt1+0x4fc>
    18f8:	bd4083a1 	ldr	s1, [x29, #128]
    18fc:	bd403fa0 	ldr	s0, [x29, #60]
    1900:	1e202820 	fadd	s0, s1, s0
    1904:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1908:	1e270001 	fmov	s1, w0
    190c:	1e210800 	fmul	s0, s0, s1
    1910:	1e202018 	fcmpe	s0, #0.0
    1914:	54000065 	b.pl	1920 <kaleido_s1_transfer_ec253tt1+0x4dc>  // b.nfrst
    1918:	52800000 	mov	w0, #0x0                   	// #0
    191c:	14000009 	b	1940 <kaleido_s1_transfer_ec253tt1+0x4fc>
    1920:	bd4083a1 	ldr	s1, [x29, #128]
    1924:	bd403fa0 	ldr	s0, [x29, #60]
    1928:	1e202820 	fadd	s0, s1, s0
    192c:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1930:	1e270001 	fmov	s1, w0
    1934:	1e210800 	fmul	s0, s0, s1
    1938:	1e390000 	fcvtzu	w0, s0
    193c:	12001c00 	and	w0, w0, #0xff
    1940:	b9808fa1 	ldrsw	x1, [x29, #140]
    1944:	91000821 	add	x1, x1, #0x2
    1948:	f94017a2 	ldr	x2, [x29, #40]
    194c:	8b010041 	add	x1, x2, x1
    1950:	39000020 	strb	w0, [x1]
    1954:	b9408fa0 	ldr	w0, [x29, #140]
    1958:	11000c00 	add	w0, w0, #0x3
    195c:	b9008fa0 	str	w0, [x29, #140]
    1960:	b94017a1 	ldr	w1, [x29, #20]
    1964:	b94013a0 	ldr	w0, [x29, #16]
    1968:	1b007c21 	mul	w1, w1, w0
    196c:	2a0103e0 	mov	w0, w1
    1970:	531f7800 	lsl	w0, w0, #1
    1974:	0b010000 	add	w0, w0, w1
    1978:	b9408fa1 	ldr	w1, [x29, #140]
    197c:	6b00003f 	cmp	w1, w0
    1980:	54ffd7ab 	b.lt	1474 <kaleido_s1_transfer_ec253tt1+0x30>  // b.tstop
    1984:	b90077bf 	str	wzr, [x29, #116]
    1988:	14000044 	b	1a98 <kaleido_s1_transfer_ec253tt1+0x654>
    198c:	b94077a1 	ldr	w1, [x29, #116]
    1990:	b9401ba0 	ldr	w0, [x29, #24]
    1994:	0b000022 	add	w2, w1, w0
    1998:	528aaac0 	mov	w0, #0x5556                	// #21846
    199c:	72aaaaa0 	movk	w0, #0x5555, lsl #16
    19a0:	9b207c40 	smull	x0, w2, w0
    19a4:	d360fc01 	lsr	x1, x0, #32
    19a8:	131f7c40 	asr	w0, w2, #31
    19ac:	4b000021 	sub	w1, w1, w0
    19b0:	2a0103e0 	mov	w0, w1
    19b4:	531f7800 	lsl	w0, w0, #1
    19b8:	0b010000 	add	w0, w0, w1
    19bc:	4b000041 	sub	w1, w2, w0
    19c0:	52800040 	mov	w0, #0x2                   	// #2
    19c4:	4b010000 	sub	w0, w0, w1
    19c8:	b9006ba0 	str	w0, [x29, #104]
    19cc:	b90073bf 	str	wzr, [x29, #112]
    19d0:	1400002b 	b	1a7c <kaleido_s1_transfer_ec253tt1+0x638>
    19d4:	b9406ba1 	ldr	w1, [x29, #104]
    19d8:	b94073a0 	ldr	w0, [x29, #112]
    19dc:	0b000021 	add	w1, w1, w0
    19e0:	b9401fa0 	ldr	w0, [x29, #28]
    19e4:	0b000021 	add	w1, w1, w0
    19e8:	528aaac0 	mov	w0, #0x5556                	// #21846
    19ec:	72aaaaa0 	movk	w0, #0x5555, lsl #16
    19f0:	9b207c20 	smull	x0, w1, w0
    19f4:	d360fc02 	lsr	x2, x0, #32
    19f8:	131f7c20 	asr	w0, w1, #31
    19fc:	4b000042 	sub	w2, w2, w0
    1a00:	2a0203e0 	mov	w0, w2
    1a04:	531f7800 	lsl	w0, w0, #1
    1a08:	0b020000 	add	w0, w0, w2
    1a0c:	4b000020 	sub	w0, w1, w0
    1a10:	b90067a0 	str	w0, [x29, #100]
    1a14:	b94077a1 	ldr	w1, [x29, #116]
    1a18:	b94017a0 	ldr	w0, [x29, #20]
    1a1c:	1b007c21 	mul	w1, w1, w0
    1a20:	b94073a0 	ldr	w0, [x29, #112]
    1a24:	0b000021 	add	w1, w1, w0
    1a28:	2a0103e0 	mov	w0, w1
    1a2c:	531f7800 	lsl	w0, w0, #1
    1a30:	0b010001 	add	w1, w0, w1
    1a34:	b94067a0 	ldr	w0, [x29, #100]
    1a38:	0b000020 	add	w0, w1, w0
    1a3c:	93407c00 	sxtw	x0, w0
    1a40:	f94017a1 	ldr	x1, [x29, #40]
    1a44:	8b000021 	add	x1, x1, x0
    1a48:	b94077a2 	ldr	w2, [x29, #116]
    1a4c:	b94017a0 	ldr	w0, [x29, #20]
    1a50:	1b007c42 	mul	w2, w2, w0
    1a54:	b94073a0 	ldr	w0, [x29, #112]
    1a58:	0b000040 	add	w0, w2, w0
    1a5c:	93407c00 	sxtw	x0, w0
    1a60:	f94013a2 	ldr	x2, [x29, #32]
    1a64:	8b000040 	add	x0, x2, x0
    1a68:	39400021 	ldrb	w1, [x1]
    1a6c:	39000001 	strb	w1, [x0]
    1a70:	b94073a0 	ldr	w0, [x29, #112]
    1a74:	11000400 	add	w0, w0, #0x1
    1a78:	b90073a0 	str	w0, [x29, #112]
    1a7c:	b94073a1 	ldr	w1, [x29, #112]
    1a80:	b94017a0 	ldr	w0, [x29, #20]
    1a84:	6b00003f 	cmp	w1, w0
    1a88:	54fffa6b 	b.lt	19d4 <kaleido_s1_transfer_ec253tt1+0x590>  // b.tstop
    1a8c:	b94077a0 	ldr	w0, [x29, #116]
    1a90:	11000400 	add	w0, w0, #0x1
    1a94:	b90077a0 	str	w0, [x29, #116]
    1a98:	b94077a1 	ldr	w1, [x29, #116]
    1a9c:	b94013a0 	ldr	w0, [x29, #16]
    1aa0:	6b00003f 	cmp	w1, w0
    1aa4:	54fff74b 	b.lt	198c <kaleido_s1_transfer_ec253tt1+0x548>  // b.tstop
    1aa8:	d503201f 	nop
    1aac:	a8c97bfd 	ldp	x29, x30, [sp], #144
    1ab0:	d65f03c0 	ret

0000000000001ab4 <adjustColor>:
    1ab4:	a9b97bfd 	stp	x29, x30, [sp, #-112]!
    1ab8:	910003fd 	mov	x29, sp
    1abc:	f90017a0 	str	x0, [x29, #40]
    1ac0:	bd0027a0 	str	s0, [x29, #36]
    1ac4:	bd0023a1 	str	s1, [x29, #32]
    1ac8:	bd001fa2 	str	s2, [x29, #28]
    1acc:	bd001ba3 	str	s3, [x29, #24]
    1ad0:	f94017a0 	ldr	x0, [x29, #40]
    1ad4:	39400000 	ldrb	w0, [x0]
    1ad8:	1e220000 	scvtf	s0, w0
    1adc:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1ae0:	1e270001 	fmov	s1, w0
    1ae4:	1e211800 	fdiv	s0, s0, s1
    1ae8:	bd005ba0 	str	s0, [x29, #88]
    1aec:	f94017a0 	ldr	x0, [x29, #40]
    1af0:	91000400 	add	x0, x0, #0x1
    1af4:	39400000 	ldrb	w0, [x0]
    1af8:	1e220000 	scvtf	s0, w0
    1afc:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1b00:	1e270001 	fmov	s1, w0
    1b04:	1e211800 	fdiv	s0, s0, s1
    1b08:	bd0057a0 	str	s0, [x29, #84]
    1b0c:	f94017a0 	ldr	x0, [x29, #40]
    1b10:	91000800 	add	x0, x0, #0x2
    1b14:	39400000 	ldrb	w0, [x0]
    1b18:	1e220000 	scvtf	s0, w0
    1b1c:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1b20:	1e270001 	fmov	s1, w0
    1b24:	1e211800 	fdiv	s0, s0, s1
    1b28:	bd0053a0 	str	s0, [x29, #80]
    1b2c:	bd4053a1 	ldr	s1, [x29, #80]
    1b30:	bd4057a0 	ldr	s0, [x29, #84]
    1b34:	97fffc77 	bl	d10 <fmaxf@plt>
    1b38:	1e204001 	fmov	s1, s0
    1b3c:	bd405ba0 	ldr	s0, [x29, #88]
    1b40:	97fffc74 	bl	d10 <fmaxf@plt>
    1b44:	bd004fa0 	str	s0, [x29, #76]
    1b48:	bd4053a1 	ldr	s1, [x29, #80]
    1b4c:	bd4057a0 	ldr	s0, [x29, #84]
    1b50:	97fffc68 	bl	cf0 <fminf@plt>
    1b54:	1e204001 	fmov	s1, s0
    1b58:	bd405ba0 	ldr	s0, [x29, #88]
    1b5c:	97fffc65 	bl	cf0 <fminf@plt>
    1b60:	bd004ba0 	str	s0, [x29, #72]
    1b64:	bd404fa1 	ldr	s1, [x29, #76]
    1b68:	bd404ba0 	ldr	s0, [x29, #72]
    1b6c:	1e203820 	fsub	s0, s1, s0
    1b70:	bd0047a0 	str	s0, [x29, #68]
    1b74:	b9006fbf 	str	wzr, [x29, #108]
    1b78:	bd404fa0 	ldr	s0, [x29, #76]
    1b7c:	bd0043a0 	str	s0, [x29, #64]
    1b80:	bd4047a0 	ldr	s0, [x29, #68]
    1b84:	5296e2e0 	mov	w0, #0xb717                	// #46871
    1b88:	72a71a20 	movk	w0, #0x38d1, lsl #16
    1b8c:	1e270001 	fmov	s1, w0
    1b90:	1e212010 	fcmpe	s0, s1
    1b94:	5400074d 	b.le	1c7c <adjustColor+0x1c8>
    1b98:	bd404fa0 	ldr	s0, [x29, #76]
    1b9c:	bd4047a1 	ldr	s1, [x29, #68]
    1ba0:	1e201820 	fdiv	s0, s1, s0
    1ba4:	bd006ba0 	str	s0, [x29, #104]
    1ba8:	bd404fa1 	ldr	s1, [x29, #76]
    1bac:	bd405ba0 	ldr	s0, [x29, #88]
    1bb0:	1e202020 	fcmp	s1, s0
    1bb4:	540001c1 	b.ne	1bec <adjustColor+0x138>  // b.any
    1bb8:	bd4057a1 	ldr	s1, [x29, #84]
    1bbc:	bd4053a0 	ldr	s0, [x29, #80]
    1bc0:	1e203821 	fsub	s1, s1, s0
    1bc4:	bd4047a0 	ldr	s0, [x29, #68]
    1bc8:	1e201820 	fdiv	s0, s1, s0
    1bcc:	1e231001 	fmov	s1, #6.000000000000000000e+00
    1bd0:	97fffc40 	bl	cd0 <fmodf@plt>
    1bd4:	1e204001 	fmov	s1, s0
    1bd8:	52a84e00 	mov	w0, #0x42700000            	// #1114636288
    1bdc:	1e270000 	fmov	s0, w0
    1be0:	1e200820 	fmul	s0, s1, s0
    1be4:	bd006fa0 	str	s0, [x29, #108]
    1be8:	1400001c 	b	1c58 <adjustColor+0x1a4>
    1bec:	bd404fa1 	ldr	s1, [x29, #76]
    1bf0:	bd4057a0 	ldr	s0, [x29, #84]
    1bf4:	1e202020 	fcmp	s1, s0
    1bf8:	540001a1 	b.ne	1c2c <adjustColor+0x178>  // b.any
    1bfc:	bd4053a1 	ldr	s1, [x29, #80]
    1c00:	bd405ba0 	ldr	s0, [x29, #88]
    1c04:	1e203821 	fsub	s1, s1, s0
    1c08:	bd4047a0 	ldr	s0, [x29, #68]
    1c0c:	1e201821 	fdiv	s1, s1, s0
    1c10:	1e201000 	fmov	s0, #2.000000000000000000e+00
    1c14:	1e202820 	fadd	s0, s1, s0
    1c18:	52a84e00 	mov	w0, #0x42700000            	// #1114636288
    1c1c:	1e270001 	fmov	s1, w0
    1c20:	1e210800 	fmul	s0, s0, s1
    1c24:	bd006fa0 	str	s0, [x29, #108]
    1c28:	1400000c 	b	1c58 <adjustColor+0x1a4>
    1c2c:	bd405ba1 	ldr	s1, [x29, #88]
    1c30:	bd4057a0 	ldr	s0, [x29, #84]
    1c34:	1e203821 	fsub	s1, s1, s0
    1c38:	bd4047a0 	ldr	s0, [x29, #68]
    1c3c:	1e201821 	fdiv	s1, s1, s0
    1c40:	1e221000 	fmov	s0, #4.000000000000000000e+00
    1c44:	1e202820 	fadd	s0, s1, s0
    1c48:	52a84e00 	mov	w0, #0x42700000            	// #1114636288
    1c4c:	1e270001 	fmov	s1, w0
    1c50:	1e210800 	fmul	s0, s0, s1
    1c54:	bd006fa0 	str	s0, [x29, #108]
    1c58:	bd406fa0 	ldr	s0, [x29, #108]
    1c5c:	1e202018 	fcmpe	s0, #0.0
    1c60:	54000125 	b.pl	1c84 <adjustColor+0x1d0>  // b.nfrst
    1c64:	bd406fa0 	ldr	s0, [x29, #108]
    1c68:	52a87680 	mov	w0, #0x43b40000            	// #1135869952
    1c6c:	1e270001 	fmov	s1, w0
    1c70:	1e212800 	fadd	s0, s0, s1
    1c74:	bd006fa0 	str	s0, [x29, #108]
    1c78:	14000003 	b	1c84 <adjustColor+0x1d0>
    1c7c:	b9006bbf 	str	wzr, [x29, #104]
    1c80:	b9006fbf 	str	wzr, [x29, #108]
    1c84:	bd406ba1 	ldr	s1, [x29, #104]
    1c88:	bd4027a0 	ldr	s0, [x29, #36]
    1c8c:	1e200820 	fmul	s0, s1, s0
    1c90:	bd006ba0 	str	s0, [x29, #104]
    1c94:	0f000401 	movi	v1.2s, #0x0
    1c98:	bd406ba0 	ldr	s0, [x29, #104]
    1c9c:	97fffc1d 	bl	d10 <fmaxf@plt>
    1ca0:	1e2e1001 	fmov	s1, #1.000000000000000000e+00
    1ca4:	97fffc13 	bl	cf0 <fminf@plt>
    1ca8:	bd006ba0 	str	s0, [x29, #104]
    1cac:	bd4043a1 	ldr	s1, [x29, #64]
    1cb0:	bd406ba0 	ldr	s0, [x29, #104]
    1cb4:	1e200820 	fmul	s0, s1, s0
    1cb8:	bd003fa0 	str	s0, [x29, #60]
    1cbc:	52a84e00 	mov	w0, #0x42700000            	// #1114636288
    1cc0:	1e270001 	fmov	s1, w0
    1cc4:	bd406fa0 	ldr	s0, [x29, #108]
    1cc8:	1e211800 	fdiv	s0, s0, s1
    1ccc:	1e201001 	fmov	s1, #2.000000000000000000e+00
    1cd0:	97fffc00 	bl	cd0 <fmodf@plt>
    1cd4:	1e204001 	fmov	s1, s0
    1cd8:	1e2e1000 	fmov	s0, #1.000000000000000000e+00
    1cdc:	1e203820 	fsub	s0, s1, s0
    1ce0:	1e20c000 	fabs	s0, s0
    1ce4:	1e2e1001 	fmov	s1, #1.000000000000000000e+00
    1ce8:	1e203820 	fsub	s0, s1, s0
    1cec:	bd403fa1 	ldr	s1, [x29, #60]
    1cf0:	1e200820 	fmul	s0, s1, s0
    1cf4:	bd003ba0 	str	s0, [x29, #56]
    1cf8:	bd4043a1 	ldr	s1, [x29, #64]
    1cfc:	bd403fa0 	ldr	s0, [x29, #60]
    1d00:	1e203820 	fsub	s0, s1, s0
    1d04:	bd0037a0 	str	s0, [x29, #52]
    1d08:	bd406fa0 	ldr	s0, [x29, #108]
    1d0c:	52a84e00 	mov	w0, #0x42700000            	// #1114636288
    1d10:	1e270001 	fmov	s1, w0
    1d14:	1e212010 	fcmpe	s0, s1
    1d18:	540000e5 	b.pl	1d34 <adjustColor+0x280>  // b.nfrst
    1d1c:	bd403fa0 	ldr	s0, [x29, #60]
    1d20:	bd0067a0 	str	s0, [x29, #100]
    1d24:	bd403ba0 	ldr	s0, [x29, #56]
    1d28:	bd0063a0 	str	s0, [x29, #96]
    1d2c:	b9005fbf 	str	wzr, [x29, #92]
    1d30:	14000032 	b	1df8 <adjustColor+0x344>
    1d34:	bd406fa0 	ldr	s0, [x29, #108]
    1d38:	52a85e00 	mov	w0, #0x42f00000            	// #1123024896
    1d3c:	1e270001 	fmov	s1, w0
    1d40:	1e212010 	fcmpe	s0, s1
    1d44:	540000e5 	b.pl	1d60 <adjustColor+0x2ac>  // b.nfrst
    1d48:	bd403ba0 	ldr	s0, [x29, #56]
    1d4c:	bd0067a0 	str	s0, [x29, #100]
    1d50:	bd403fa0 	ldr	s0, [x29, #60]
    1d54:	bd0063a0 	str	s0, [x29, #96]
    1d58:	b9005fbf 	str	wzr, [x29, #92]
    1d5c:	14000027 	b	1df8 <adjustColor+0x344>
    1d60:	bd406fa0 	ldr	s0, [x29, #108]
    1d64:	52a86680 	mov	w0, #0x43340000            	// #1127481344
    1d68:	1e270001 	fmov	s1, w0
    1d6c:	1e212010 	fcmpe	s0, s1
    1d70:	540000e5 	b.pl	1d8c <adjustColor+0x2d8>  // b.nfrst
    1d74:	b90067bf 	str	wzr, [x29, #100]
    1d78:	bd403fa0 	ldr	s0, [x29, #60]
    1d7c:	bd0063a0 	str	s0, [x29, #96]
    1d80:	bd403ba0 	ldr	s0, [x29, #56]
    1d84:	bd005fa0 	str	s0, [x29, #92]
    1d88:	1400001c 	b	1df8 <adjustColor+0x344>
    1d8c:	bd406fa0 	ldr	s0, [x29, #108]
    1d90:	52a86e00 	mov	w0, #0x43700000            	// #1131413504
    1d94:	1e270001 	fmov	s1, w0
    1d98:	1e212010 	fcmpe	s0, s1
    1d9c:	540000e5 	b.pl	1db8 <adjustColor+0x304>  // b.nfrst
    1da0:	b90067bf 	str	wzr, [x29, #100]
    1da4:	bd403ba0 	ldr	s0, [x29, #56]
    1da8:	bd0063a0 	str	s0, [x29, #96]
    1dac:	bd403fa0 	ldr	s0, [x29, #60]
    1db0:	bd005fa0 	str	s0, [x29, #92]
    1db4:	14000011 	b	1df8 <adjustColor+0x344>
    1db8:	bd406fa0 	ldr	s0, [x29, #108]
    1dbc:	52a872c0 	mov	w0, #0x43960000            	// #1133903872
    1dc0:	1e270001 	fmov	s1, w0
    1dc4:	1e212010 	fcmpe	s0, s1
    1dc8:	540000e5 	b.pl	1de4 <adjustColor+0x330>  // b.nfrst
    1dcc:	bd403ba0 	ldr	s0, [x29, #56]
    1dd0:	bd0067a0 	str	s0, [x29, #100]
    1dd4:	b90063bf 	str	wzr, [x29, #96]
    1dd8:	bd403fa0 	ldr	s0, [x29, #60]
    1ddc:	bd005fa0 	str	s0, [x29, #92]
    1de0:	14000006 	b	1df8 <adjustColor+0x344>
    1de4:	bd403fa0 	ldr	s0, [x29, #60]
    1de8:	bd0067a0 	str	s0, [x29, #100]
    1dec:	b90063bf 	str	wzr, [x29, #96]
    1df0:	bd403ba0 	ldr	s0, [x29, #56]
    1df4:	bd005fa0 	str	s0, [x29, #92]
    1df8:	bd4067a1 	ldr	s1, [x29, #100]
    1dfc:	bd4037a0 	ldr	s0, [x29, #52]
    1e00:	1e202820 	fadd	s0, s1, s0
    1e04:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1e08:	1e270001 	fmov	s1, w0
    1e0c:	1e210800 	fmul	s0, s0, s1
    1e10:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1e14:	1e270001 	fmov	s1, w0
    1e18:	1e212010 	fcmpe	s0, s1
    1e1c:	5400006d 	b.le	1e28 <adjustColor+0x374>
    1e20:	52801fe0 	mov	w0, #0xff                  	// #255
    1e24:	14000013 	b	1e70 <adjustColor+0x3bc>
    1e28:	bd4067a1 	ldr	s1, [x29, #100]
    1e2c:	bd4037a0 	ldr	s0, [x29, #52]
    1e30:	1e202820 	fadd	s0, s1, s0
    1e34:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1e38:	1e270001 	fmov	s1, w0
    1e3c:	1e210800 	fmul	s0, s0, s1
    1e40:	1e202018 	fcmpe	s0, #0.0
    1e44:	54000065 	b.pl	1e50 <adjustColor+0x39c>  // b.nfrst
    1e48:	52800000 	mov	w0, #0x0                   	// #0
    1e4c:	14000009 	b	1e70 <adjustColor+0x3bc>
    1e50:	bd4067a1 	ldr	s1, [x29, #100]
    1e54:	bd4037a0 	ldr	s0, [x29, #52]
    1e58:	1e202820 	fadd	s0, s1, s0
    1e5c:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1e60:	1e270001 	fmov	s1, w0
    1e64:	1e210800 	fmul	s0, s0, s1
    1e68:	1e390000 	fcvtzu	w0, s0
    1e6c:	12001c00 	and	w0, w0, #0xff
    1e70:	f94017a1 	ldr	x1, [x29, #40]
    1e74:	39000020 	strb	w0, [x1]
    1e78:	bd4063a1 	ldr	s1, [x29, #96]
    1e7c:	bd4037a0 	ldr	s0, [x29, #52]
    1e80:	1e202820 	fadd	s0, s1, s0
    1e84:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1e88:	1e270001 	fmov	s1, w0
    1e8c:	1e210800 	fmul	s0, s0, s1
    1e90:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1e94:	1e270001 	fmov	s1, w0
    1e98:	1e212010 	fcmpe	s0, s1
    1e9c:	5400006d 	b.le	1ea8 <adjustColor+0x3f4>
    1ea0:	52801fe0 	mov	w0, #0xff                  	// #255
    1ea4:	14000013 	b	1ef0 <adjustColor+0x43c>
    1ea8:	bd4063a1 	ldr	s1, [x29, #96]
    1eac:	bd4037a0 	ldr	s0, [x29, #52]
    1eb0:	1e202820 	fadd	s0, s1, s0
    1eb4:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1eb8:	1e270001 	fmov	s1, w0
    1ebc:	1e210800 	fmul	s0, s0, s1
    1ec0:	1e202018 	fcmpe	s0, #0.0
    1ec4:	54000065 	b.pl	1ed0 <adjustColor+0x41c>  // b.nfrst
    1ec8:	52800000 	mov	w0, #0x0                   	// #0
    1ecc:	14000009 	b	1ef0 <adjustColor+0x43c>
    1ed0:	bd4063a1 	ldr	s1, [x29, #96]
    1ed4:	bd4037a0 	ldr	s0, [x29, #52]
    1ed8:	1e202820 	fadd	s0, s1, s0
    1edc:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1ee0:	1e270001 	fmov	s1, w0
    1ee4:	1e210800 	fmul	s0, s0, s1
    1ee8:	1e390000 	fcvtzu	w0, s0
    1eec:	12001c00 	and	w0, w0, #0xff
    1ef0:	f94017a1 	ldr	x1, [x29, #40]
    1ef4:	91000421 	add	x1, x1, #0x1
    1ef8:	39000020 	strb	w0, [x1]
    1efc:	bd405fa1 	ldr	s1, [x29, #92]
    1f00:	bd4037a0 	ldr	s0, [x29, #52]
    1f04:	1e202820 	fadd	s0, s1, s0
    1f08:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1f0c:	1e270001 	fmov	s1, w0
    1f10:	1e210800 	fmul	s0, s0, s1
    1f14:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1f18:	1e270001 	fmov	s1, w0
    1f1c:	1e212010 	fcmpe	s0, s1
    1f20:	5400006d 	b.le	1f2c <adjustColor+0x478>
    1f24:	52801fe0 	mov	w0, #0xff                  	// #255
    1f28:	14000013 	b	1f74 <adjustColor+0x4c0>
    1f2c:	bd405fa1 	ldr	s1, [x29, #92]
    1f30:	bd4037a0 	ldr	s0, [x29, #52]
    1f34:	1e202820 	fadd	s0, s1, s0
    1f38:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1f3c:	1e270001 	fmov	s1, w0
    1f40:	1e210800 	fmul	s0, s0, s1
    1f44:	1e202018 	fcmpe	s0, #0.0
    1f48:	54000065 	b.pl	1f54 <adjustColor+0x4a0>  // b.nfrst
    1f4c:	52800000 	mov	w0, #0x0                   	// #0
    1f50:	14000009 	b	1f74 <adjustColor+0x4c0>
    1f54:	bd405fa1 	ldr	s1, [x29, #92]
    1f58:	bd4037a0 	ldr	s0, [x29, #52]
    1f5c:	1e202820 	fadd	s0, s1, s0
    1f60:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    1f64:	1e270001 	fmov	s1, w0
    1f68:	1e210800 	fmul	s0, s0, s1
    1f6c:	1e390000 	fcvtzu	w0, s0
    1f70:	12001c00 	and	w0, w0, #0xff
    1f74:	f94017a1 	ldr	x1, [x29, #40]
    1f78:	91000821 	add	x1, x1, #0x2
    1f7c:	39000020 	strb	w0, [x1]
    1f80:	d503201f 	nop
    1f84:	a8c77bfd 	ldp	x29, x30, [sp], #112
    1f88:	d65f03c0 	ret

0000000000001f8c <ImageProcess_ColorEnhace>:
    1f8c:	a9bc7bfd 	stp	x29, x30, [sp, #-64]!
    1f90:	910003fd 	mov	x29, sp
    1f94:	bd002fa0 	str	s0, [x29, #44]
    1f98:	bd002ba1 	str	s1, [x29, #40]
    1f9c:	bd0027a2 	str	s2, [x29, #36]
    1fa0:	bd0023a3 	str	s3, [x29, #32]
    1fa4:	f9000fa0 	str	x0, [x29, #24]
    1fa8:	b90017a1 	str	w1, [x29, #20]
    1fac:	b90013a2 	str	w2, [x29, #16]
    1fb0:	b9003fbf 	str	wzr, [x29, #60]
    1fb4:	14000010 	b	1ff4 <ImageProcess_ColorEnhace+0x68>
    1fb8:	b9403fa1 	ldr	w1, [x29, #60]
    1fbc:	2a0103e0 	mov	w0, w1
    1fc0:	531f7800 	lsl	w0, w0, #1
    1fc4:	0b010000 	add	w0, w0, w1
    1fc8:	93407c00 	sxtw	x0, w0
    1fcc:	f9400fa1 	ldr	x1, [x29, #24]
    1fd0:	8b000020 	add	x0, x1, x0
    1fd4:	bd4027a3 	ldr	s3, [x29, #36]
    1fd8:	bd4023a2 	ldr	s2, [x29, #32]
    1fdc:	bd402ba1 	ldr	s1, [x29, #40]
    1fe0:	bd402fa0 	ldr	s0, [x29, #44]
    1fe4:	97fffeb4 	bl	1ab4 <adjustColor>
    1fe8:	b9403fa0 	ldr	w0, [x29, #60]
    1fec:	11000400 	add	w0, w0, #0x1
    1ff0:	b9003fa0 	str	w0, [x29, #60]
    1ff4:	b94017a1 	ldr	w1, [x29, #20]
    1ff8:	b94013a0 	ldr	w0, [x29, #16]
    1ffc:	1b007c20 	mul	w0, w1, w0
    2000:	b9403fa1 	ldr	w1, [x29, #60]
    2004:	6b00003f 	cmp	w1, w0
    2008:	54fffd8b 	b.lt	1fb8 <ImageProcess_ColorEnhace+0x2c>  // b.tstop
    200c:	d503201f 	nop
    2010:	a8c47bfd 	ldp	x29, x30, [sp], #64
    2014:	d65f03c0 	ret

0000000000002018 <find_nearest_color_AIO>:
    2018:	d100c3ff 	sub	sp, sp, #0x30
    201c:	b9000fe0 	str	w0, [sp, #12]
    2020:	b9000be1 	str	w1, [sp, #8]
    2024:	b90007e2 	str	w2, [sp, #4]
    2028:	12b00000 	mov	w0, #0x7fffffff            	// #2147483647
    202c:	b9002fe0 	str	w0, [sp, #44]
    2030:	b9002bff 	str	wzr, [sp, #40]
    2034:	b90027ff 	str	wzr, [sp, #36]
    2038:	1400003c 	b	2128 <find_nearest_color_AIO+0x110>
    203c:	b9400fe0 	ldr	w0, [sp, #12]
    2040:	12001c02 	and	w2, w0, #0xff
    2044:	b0000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    2048:	f947e403 	ldr	x3, [x0, #4040]
    204c:	b98027e1 	ldrsw	x1, [sp, #36]
    2050:	aa0103e0 	mov	x0, x1
    2054:	d37ff800 	lsl	x0, x0, #1
    2058:	8b010000 	add	x0, x0, x1
    205c:	8b000060 	add	x0, x3, x0
    2060:	39400000 	ldrb	w0, [x0]
    2064:	4b000040 	sub	w0, w2, w0
    2068:	b90023e0 	str	w0, [sp, #32]
    206c:	b9400be0 	ldr	w0, [sp, #8]
    2070:	12001c02 	and	w2, w0, #0xff
    2074:	b0000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    2078:	f947e403 	ldr	x3, [x0, #4040]
    207c:	b98027e1 	ldrsw	x1, [sp, #36]
    2080:	aa0103e0 	mov	x0, x1
    2084:	d37ff800 	lsl	x0, x0, #1
    2088:	8b010000 	add	x0, x0, x1
    208c:	8b000060 	add	x0, x3, x0
    2090:	39400400 	ldrb	w0, [x0, #1]
    2094:	4b000040 	sub	w0, w2, w0
    2098:	b9001fe0 	str	w0, [sp, #28]
    209c:	b94007e0 	ldr	w0, [sp, #4]
    20a0:	12001c02 	and	w2, w0, #0xff
    20a4:	b0000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    20a8:	f947e403 	ldr	x3, [x0, #4040]
    20ac:	b98027e1 	ldrsw	x1, [sp, #36]
    20b0:	aa0103e0 	mov	x0, x1
    20b4:	d37ff800 	lsl	x0, x0, #1
    20b8:	8b010000 	add	x0, x0, x1
    20bc:	8b000060 	add	x0, x3, x0
    20c0:	39400800 	ldrb	w0, [x0, #2]
    20c4:	4b000040 	sub	w0, w2, w0
    20c8:	b9001be0 	str	w0, [sp, #24]
    20cc:	b94023e1 	ldr	w1, [sp, #32]
    20d0:	b94023e0 	ldr	w0, [sp, #32]
    20d4:	1b007c21 	mul	w1, w1, w0
    20d8:	b9401fe2 	ldr	w2, [sp, #28]
    20dc:	b9401fe0 	ldr	w0, [sp, #28]
    20e0:	1b007c40 	mul	w0, w2, w0
    20e4:	0b000021 	add	w1, w1, w0
    20e8:	b9401be2 	ldr	w2, [sp, #24]
    20ec:	b9401be0 	ldr	w0, [sp, #24]
    20f0:	1b007c40 	mul	w0, w2, w0
    20f4:	0b000020 	add	w0, w1, w0
    20f8:	b90017e0 	str	w0, [sp, #20]
    20fc:	b94017e1 	ldr	w1, [sp, #20]
    2100:	b9402fe0 	ldr	w0, [sp, #44]
    2104:	6b00003f 	cmp	w1, w0
    2108:	540000aa 	b.ge	211c <find_nearest_color_AIO+0x104>  // b.tcont
    210c:	b94017e0 	ldr	w0, [sp, #20]
    2110:	b9002fe0 	str	w0, [sp, #44]
    2114:	b94027e0 	ldr	w0, [sp, #36]
    2118:	b9002be0 	str	w0, [sp, #40]
    211c:	b94027e0 	ldr	w0, [sp, #36]
    2120:	11000400 	add	w0, w0, #0x1
    2124:	b90027e0 	str	w0, [sp, #36]
    2128:	b94027e0 	ldr	w0, [sp, #36]
    212c:	7100141f 	cmp	w0, #0x5
    2130:	54fff86d 	b.le	203c <find_nearest_color_AIO+0x24>
    2134:	b9402be0 	ldr	w0, [sp, #40]
    2138:	12001c00 	and	w0, w0, #0xff
    213c:	9100c3ff 	add	sp, sp, #0x30
    2140:	d65f03c0 	ret

0000000000002144 <ImageProcess_Dither_AIO>:
    2144:	a9b17bfd 	stp	x29, x30, [sp, #-240]!
    2148:	910003fd 	mov	x29, sp
    214c:	f9000fa0 	str	x0, [x29, #24]
    2150:	b90017a1 	str	w1, [x29, #20]
    2154:	b90013a2 	str	w2, [x29, #16]
    2158:	b900efbf 	str	wzr, [x29, #236]
    215c:	b98017a1 	ldrsw	x1, [x29, #20]
    2160:	aa0103e0 	mov	x0, x1
    2164:	d37ff800 	lsl	x0, x0, #1
    2168:	8b010000 	add	x0, x0, x1
    216c:	d37ff800 	lsl	x0, x0, #1
    2170:	97fffad4 	bl	cc0 <malloc@plt>
    2174:	f9004fa0 	str	x0, [x29, #152]
    2178:	b98017a1 	ldrsw	x1, [x29, #20]
    217c:	aa0103e0 	mov	x0, x1
    2180:	d37ff800 	lsl	x0, x0, #1
    2184:	8b010000 	add	x0, x0, x1
    2188:	d37ff800 	lsl	x0, x0, #1
    218c:	97fffacd 	bl	cc0 <malloc@plt>
    2190:	f90053a0 	str	x0, [x29, #160]
    2194:	b98017a1 	ldrsw	x1, [x29, #20]
    2198:	aa0103e0 	mov	x0, x1
    219c:	d37ff800 	lsl	x0, x0, #1
    21a0:	8b010000 	add	x0, x0, x1
    21a4:	d37ff800 	lsl	x0, x0, #1
    21a8:	97fffac6 	bl	cc0 <malloc@plt>
    21ac:	f90057a0 	str	x0, [x29, #168]
    21b0:	f9404fa3 	ldr	x3, [x29, #152]
    21b4:	b98017a1 	ldrsw	x1, [x29, #20]
    21b8:	aa0103e0 	mov	x0, x1
    21bc:	d37ff800 	lsl	x0, x0, #1
    21c0:	8b010000 	add	x0, x0, x1
    21c4:	d37ff800 	lsl	x0, x0, #1
    21c8:	aa0003e2 	mov	x2, x0
    21cc:	52800001 	mov	w1, #0x0                   	// #0
    21d0:	aa0303e0 	mov	x0, x3
    21d4:	97fffac3 	bl	ce0 <memset@plt>
    21d8:	f94053a3 	ldr	x3, [x29, #160]
    21dc:	b98017a1 	ldrsw	x1, [x29, #20]
    21e0:	aa0103e0 	mov	x0, x1
    21e4:	d37ff800 	lsl	x0, x0, #1
    21e8:	8b010000 	add	x0, x0, x1
    21ec:	d37ff800 	lsl	x0, x0, #1
    21f0:	aa0003e2 	mov	x2, x0
    21f4:	52800001 	mov	w1, #0x0                   	// #0
    21f8:	aa0303e0 	mov	x0, x3
    21fc:	97fffab9 	bl	ce0 <memset@plt>
    2200:	f94057a3 	ldr	x3, [x29, #168]
    2204:	b98017a1 	ldrsw	x1, [x29, #20]
    2208:	aa0103e0 	mov	x0, x1
    220c:	d37ff800 	lsl	x0, x0, #1
    2210:	8b010000 	add	x0, x0, x1
    2214:	d37ff800 	lsl	x0, x0, #1
    2218:	aa0003e2 	mov	x2, x0
    221c:	52800001 	mov	w1, #0x0                   	// #0
    2220:	aa0303e0 	mov	x0, x3
    2224:	97fffaaf 	bl	ce0 <memset@plt>
    2228:	f9404fa0 	ldr	x0, [x29, #152]
    222c:	f100001f 	cmp	x0, #0x0
    2230:	540000a1 	b.ne	2244 <ImageProcess_Dither_AIO+0x100>  // b.any
    2234:	b0000000 	adrp	x0, 3000 <ImageProcess_Dither_T2001+0x70>
    2238:	912b0000 	add	x0, x0, #0xac0
    223c:	97fffab9 	bl	d20 <puts@plt>
    2240:	14000197 	b	289c <ImageProcess_Dither_AIO+0x758>
    2244:	f94053a0 	ldr	x0, [x29, #160]
    2248:	f100001f 	cmp	x0, #0x0
    224c:	540000a1 	b.ne	2260 <ImageProcess_Dither_AIO+0x11c>  // b.any
    2250:	b0000000 	adrp	x0, 3000 <ImageProcess_Dither_T2001+0x70>
    2254:	912b0000 	add	x0, x0, #0xac0
    2258:	97fffab2 	bl	d20 <puts@plt>
    225c:	14000190 	b	289c <ImageProcess_Dither_AIO+0x758>
    2260:	f94057a0 	ldr	x0, [x29, #168]
    2264:	f100001f 	cmp	x0, #0x0
    2268:	540000a1 	b.ne	227c <ImageProcess_Dither_AIO+0x138>  // b.any
    226c:	b0000000 	adrp	x0, 3000 <ImageProcess_Dither_T2001+0x70>
    2270:	912b0000 	add	x0, x0, #0xac0
    2274:	97fffaab 	bl	d20 <puts@plt>
    2278:	14000189 	b	289c <ImageProcess_Dither_AIO+0x758>
    227c:	b900ebbf 	str	wzr, [x29, #232]
    2280:	1400017d 	b	2874 <ImageProcess_Dither_AIO+0x730>
    2284:	b900e7bf 	str	wzr, [x29, #228]
    2288:	14000154 	b	27d8 <ImageProcess_Dither_AIO+0x694>
    228c:	b980efa0 	ldrsw	x0, [x29, #236]
    2290:	f9400fa1 	ldr	x1, [x29, #24]
    2294:	8b000020 	add	x0, x1, x0
    2298:	39400000 	ldrb	w0, [x0]
    229c:	2a0003e3 	mov	w3, w0
    22a0:	f9404fa2 	ldr	x2, [x29, #152]
    22a4:	b980e7a1 	ldrsw	x1, [x29, #228]
    22a8:	aa0103e0 	mov	x0, x1
    22ac:	d37ff800 	lsl	x0, x0, #1
    22b0:	8b010000 	add	x0, x0, x1
    22b4:	d37ff800 	lsl	x0, x0, #1
    22b8:	8b000040 	add	x0, x2, x0
    22bc:	79c00000 	ldrsh	w0, [x0]
    22c0:	0b000060 	add	w0, w3, w0
    22c4:	b900dfa0 	str	w0, [x29, #220]
    22c8:	b980efa0 	ldrsw	x0, [x29, #236]
    22cc:	91000400 	add	x0, x0, #0x1
    22d0:	f9400fa1 	ldr	x1, [x29, #24]
    22d4:	8b000020 	add	x0, x1, x0
    22d8:	39400000 	ldrb	w0, [x0]
    22dc:	2a0003e3 	mov	w3, w0
    22e0:	f9404fa2 	ldr	x2, [x29, #152]
    22e4:	b980e7a1 	ldrsw	x1, [x29, #228]
    22e8:	aa0103e0 	mov	x0, x1
    22ec:	d37ff800 	lsl	x0, x0, #1
    22f0:	8b010000 	add	x0, x0, x1
    22f4:	d37ff800 	lsl	x0, x0, #1
    22f8:	8b000040 	add	x0, x2, x0
    22fc:	79c00400 	ldrsh	w0, [x0, #2]
    2300:	0b000060 	add	w0, w3, w0
    2304:	b900dba0 	str	w0, [x29, #216]
    2308:	b980efa0 	ldrsw	x0, [x29, #236]
    230c:	91000800 	add	x0, x0, #0x2
    2310:	f9400fa1 	ldr	x1, [x29, #24]
    2314:	8b000020 	add	x0, x1, x0
    2318:	39400000 	ldrb	w0, [x0]
    231c:	2a0003e3 	mov	w3, w0
    2320:	f9404fa2 	ldr	x2, [x29, #152]
    2324:	b980e7a1 	ldrsw	x1, [x29, #228]
    2328:	aa0103e0 	mov	x0, x1
    232c:	d37ff800 	lsl	x0, x0, #1
    2330:	8b010000 	add	x0, x0, x1
    2334:	d37ff800 	lsl	x0, x0, #1
    2338:	8b000040 	add	x0, x2, x0
    233c:	79c00800 	ldrsh	w0, [x0, #4]
    2340:	0b000060 	add	w0, w3, w0
    2344:	b900d7a0 	str	w0, [x29, #212]
    2348:	b940dfa0 	ldr	w0, [x29, #220]
    234c:	1e220000 	scvtf	s0, w0
    2350:	0f000401 	movi	v1.2s, #0x0
    2354:	97fffa6f 	bl	d10 <fmaxf@plt>
    2358:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    235c:	1e270001 	fmov	s1, w0
    2360:	97fffa64 	bl	cf0 <fminf@plt>
    2364:	1e380000 	fcvtzs	w0, s0
    2368:	b900dfa0 	str	w0, [x29, #220]
    236c:	b940dba0 	ldr	w0, [x29, #216]
    2370:	1e220000 	scvtf	s0, w0
    2374:	0f000401 	movi	v1.2s, #0x0
    2378:	97fffa66 	bl	d10 <fmaxf@plt>
    237c:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    2380:	1e270001 	fmov	s1, w0
    2384:	97fffa5b 	bl	cf0 <fminf@plt>
    2388:	1e380000 	fcvtzs	w0, s0
    238c:	b900dba0 	str	w0, [x29, #216]
    2390:	b940d7a0 	ldr	w0, [x29, #212]
    2394:	1e220000 	scvtf	s0, w0
    2398:	0f000401 	movi	v1.2s, #0x0
    239c:	97fffa5d 	bl	d10 <fmaxf@plt>
    23a0:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    23a4:	1e270001 	fmov	s1, w0
    23a8:	97fffa52 	bl	cf0 <fminf@plt>
    23ac:	1e380000 	fcvtzs	w0, s0
    23b0:	b900d7a0 	str	w0, [x29, #212]
    23b4:	b940d7a2 	ldr	w2, [x29, #212]
    23b8:	b940dba1 	ldr	w1, [x29, #216]
    23bc:	b940dfa0 	ldr	w0, [x29, #220]
    23c0:	97ffff16 	bl	2018 <find_nearest_color_AIO>
    23c4:	39034fa0 	strb	w0, [x29, #211]
    23c8:	39434fa1 	ldrb	w1, [x29, #211]
    23cc:	b0000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    23d0:	f947e402 	ldr	x2, [x0, #4040]
    23d4:	93407c21 	sxtw	x1, w1
    23d8:	aa0103e0 	mov	x0, x1
    23dc:	d37ff800 	lsl	x0, x0, #1
    23e0:	8b010000 	add	x0, x0, x1
    23e4:	8b000040 	add	x0, x2, x0
    23e8:	39400000 	ldrb	w0, [x0]
    23ec:	39034ba0 	strb	w0, [x29, #210]
    23f0:	39434fa1 	ldrb	w1, [x29, #211]
    23f4:	b0000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    23f8:	f947e402 	ldr	x2, [x0, #4040]
    23fc:	93407c21 	sxtw	x1, w1
    2400:	aa0103e0 	mov	x0, x1
    2404:	d37ff800 	lsl	x0, x0, #1
    2408:	8b010000 	add	x0, x0, x1
    240c:	8b000040 	add	x0, x2, x0
    2410:	39400400 	ldrb	w0, [x0, #1]
    2414:	390347a0 	strb	w0, [x29, #209]
    2418:	39434fa1 	ldrb	w1, [x29, #211]
    241c:	b0000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    2420:	f947e402 	ldr	x2, [x0, #4040]
    2424:	93407c21 	sxtw	x1, w1
    2428:	aa0103e0 	mov	x0, x1
    242c:	d37ff800 	lsl	x0, x0, #1
    2430:	8b010000 	add	x0, x0, x1
    2434:	8b000040 	add	x0, x2, x0
    2438:	39400800 	ldrb	w0, [x0, #2]
    243c:	390343a0 	strb	w0, [x29, #208]
    2440:	b980efa0 	ldrsw	x0, [x29, #236]
    2444:	f9400fa1 	ldr	x1, [x29, #24]
    2448:	8b000020 	add	x0, x1, x0
    244c:	39434ba1 	ldrb	w1, [x29, #210]
    2450:	39000001 	strb	w1, [x0]
    2454:	b980efa0 	ldrsw	x0, [x29, #236]
    2458:	91000400 	add	x0, x0, #0x1
    245c:	f9400fa1 	ldr	x1, [x29, #24]
    2460:	8b000020 	add	x0, x1, x0
    2464:	394347a1 	ldrb	w1, [x29, #209]
    2468:	39000001 	strb	w1, [x0]
    246c:	b980efa0 	ldrsw	x0, [x29, #236]
    2470:	91000800 	add	x0, x0, #0x2
    2474:	f9400fa1 	ldr	x1, [x29, #24]
    2478:	8b000020 	add	x0, x1, x0
    247c:	394343a1 	ldrb	w1, [x29, #208]
    2480:	39000001 	strb	w1, [x0]
    2484:	b940efa0 	ldr	w0, [x29, #236]
    2488:	11000c00 	add	w0, w0, #0x3
    248c:	b900efa0 	str	w0, [x29, #236]
    2490:	39434ba0 	ldrb	w0, [x29, #210]
    2494:	b940dfa1 	ldr	w1, [x29, #220]
    2498:	4b000020 	sub	w0, w1, w0
    249c:	b900cfa0 	str	w0, [x29, #204]
    24a0:	394347a0 	ldrb	w0, [x29, #209]
    24a4:	b940dba1 	ldr	w1, [x29, #216]
    24a8:	4b000020 	sub	w0, w1, w0
    24ac:	b900cba0 	str	w0, [x29, #200]
    24b0:	394343a0 	ldrb	w0, [x29, #208]
    24b4:	b940d7a1 	ldr	w1, [x29, #212]
    24b8:	4b000020 	sub	w0, w1, w0
    24bc:	b900c7a0 	str	w0, [x29, #196]
    24c0:	b0000000 	adrp	x0, 3000 <ImageProcess_Dither_T2001+0x70>
    24c4:	912b6000 	add	x0, x0, #0xad8
    24c8:	910203a2 	add	x2, x29, #0x80
    24cc:	aa0003e3 	mov	x3, x0
    24d0:	a9400460 	ldp	x0, x1, [x3]
    24d4:	a9000440 	stp	x0, x1, [x2]
    24d8:	f9400860 	ldr	x0, [x3, #16]
    24dc:	f9000840 	str	x0, [x2, #16]
    24e0:	b0000000 	adrp	x0, 3000 <ImageProcess_Dither_T2001+0x70>
    24e4:	912bc001 	add	x1, x0, #0xaf0
    24e8:	910083a0 	add	x0, x29, #0x20
    24ec:	a9400c22 	ldp	x2, x3, [x1]
    24f0:	a9000c02 	stp	x2, x3, [x0]
    24f4:	a9410c22 	ldp	x2, x3, [x1, #16]
    24f8:	a9010c02 	stp	x2, x3, [x0, #16]
    24fc:	a9420c22 	ldp	x2, x3, [x1, #32]
    2500:	a9020c02 	stp	x2, x3, [x0, #32]
    2504:	a9430c22 	ldp	x2, x3, [x1, #48]
    2508:	a9030c02 	stp	x2, x3, [x0, #48]
    250c:	a9440c22 	ldp	x2, x3, [x1, #64]
    2510:	a9040c02 	stp	x2, x3, [x0, #64]
    2514:	a9450821 	ldp	x1, x2, [x1, #80]
    2518:	a9050801 	stp	x1, x2, [x0, #80]
    251c:	b900e3bf 	str	wzr, [x29, #224]
    2520:	140000a8 	b	27c0 <ImageProcess_Dither_AIO+0x67c>
    2524:	b940e3a0 	ldr	w0, [x29, #224]
    2528:	531f7800 	lsl	w0, w0, #1
    252c:	93407c00 	sxtw	x0, w0
    2530:	d37ef400 	lsl	x0, x0, #2
    2534:	910083a1 	add	x1, x29, #0x20
    2538:	b8606820 	ldr	w0, [x1, x0]
    253c:	b900c3a0 	str	w0, [x29, #192]
    2540:	b940e3a0 	ldr	w0, [x29, #224]
    2544:	531f7800 	lsl	w0, w0, #1
    2548:	11000400 	add	w0, w0, #0x1
    254c:	93407c00 	sxtw	x0, w0
    2550:	d37ef400 	lsl	x0, x0, #2
    2554:	910083a1 	add	x1, x29, #0x20
    2558:	b8606820 	ldr	w0, [x1, x0]
    255c:	b900bfa0 	str	w0, [x29, #188]
    2560:	b980e3a0 	ldrsw	x0, [x29, #224]
    2564:	d37ff800 	lsl	x0, x0, #1
    2568:	910203a1 	add	x1, x29, #0x80
    256c:	78e06820 	ldrsh	w0, [x1, x0]
    2570:	b900bba0 	str	w0, [x29, #184]
    2574:	b940e7a1 	ldr	w1, [x29, #228]
    2578:	b940c3a0 	ldr	w0, [x29, #192]
    257c:	0b000020 	add	w0, w1, w0
    2580:	7100001f 	cmp	w0, #0x0
    2584:	5400112b 	b.lt	27a8 <ImageProcess_Dither_AIO+0x664>  // b.tstop
    2588:	b940e7a1 	ldr	w1, [x29, #228]
    258c:	b940c3a0 	ldr	w0, [x29, #192]
    2590:	0b000020 	add	w0, w1, w0
    2594:	b94017a1 	ldr	w1, [x29, #20]
    2598:	6b00003f 	cmp	w1, w0
    259c:	5400106d 	b.le	27a8 <ImageProcess_Dither_AIO+0x664>
    25a0:	b940eba1 	ldr	w1, [x29, #232]
    25a4:	b940bfa0 	ldr	w0, [x29, #188]
    25a8:	0b000020 	add	w0, w1, w0
    25ac:	b94013a1 	ldr	w1, [x29, #16]
    25b0:	6b00003f 	cmp	w1, w0
    25b4:	54000fed 	b.le	27b0 <ImageProcess_Dither_AIO+0x66c>
    25b8:	b940bfa0 	ldr	w0, [x29, #188]
    25bc:	7100001f 	cmp	w0, #0x0
    25c0:	54000100 	b.eq	25e0 <ImageProcess_Dither_AIO+0x49c>  // b.none
    25c4:	b940bfa0 	ldr	w0, [x29, #188]
    25c8:	7100041f 	cmp	w0, #0x1
    25cc:	54000061 	b.ne	25d8 <ImageProcess_Dither_AIO+0x494>  // b.any
    25d0:	52800020 	mov	w0, #0x1                   	// #1
    25d4:	14000004 	b	25e4 <ImageProcess_Dither_AIO+0x4a0>
    25d8:	52800040 	mov	w0, #0x2                   	// #2
    25dc:	14000002 	b	25e4 <ImageProcess_Dither_AIO+0x4a0>
    25e0:	52800000 	mov	w0, #0x0                   	// #0
    25e4:	b900b7a0 	str	w0, [x29, #180]
    25e8:	b980b7a0 	ldrsw	x0, [x29, #180]
    25ec:	d37df000 	lsl	x0, x0, #3
    25f0:	910263a1 	add	x1, x29, #0x98
    25f4:	f8606822 	ldr	x2, [x1, x0]
    25f8:	b940e7a1 	ldr	w1, [x29, #228]
    25fc:	b940c3a0 	ldr	w0, [x29, #192]
    2600:	0b000020 	add	w0, w1, w0
    2604:	93407c01 	sxtw	x1, w0
    2608:	aa0103e0 	mov	x0, x1
    260c:	d37ff800 	lsl	x0, x0, #1
    2610:	8b010000 	add	x0, x0, x1
    2614:	d37ff800 	lsl	x0, x0, #1
    2618:	8b000040 	add	x0, x2, x0
    261c:	79c00000 	ldrsh	w0, [x0]
    2620:	12003c01 	and	w1, w0, #0xffff
    2624:	b940cfa2 	ldr	w2, [x29, #204]
    2628:	b940bba0 	ldr	w0, [x29, #184]
    262c:	1b007c40 	mul	w0, w2, w0
    2630:	13067c00 	asr	w0, w0, #6
    2634:	12003c00 	and	w0, w0, #0xffff
    2638:	0b000020 	add	w0, w1, w0
    263c:	12003c03 	and	w3, w0, #0xffff
    2640:	b980b7a0 	ldrsw	x0, [x29, #180]
    2644:	d37df000 	lsl	x0, x0, #3
    2648:	910263a1 	add	x1, x29, #0x98
    264c:	f8606822 	ldr	x2, [x1, x0]
    2650:	b940e7a1 	ldr	w1, [x29, #228]
    2654:	b940c3a0 	ldr	w0, [x29, #192]
    2658:	0b000020 	add	w0, w1, w0
    265c:	93407c01 	sxtw	x1, w0
    2660:	aa0103e0 	mov	x0, x1
    2664:	d37ff800 	lsl	x0, x0, #1
    2668:	8b010000 	add	x0, x0, x1
    266c:	d37ff800 	lsl	x0, x0, #1
    2670:	8b000040 	add	x0, x2, x0
    2674:	13003c61 	sxth	w1, w3
    2678:	79000001 	strh	w1, [x0]
    267c:	b980b7a0 	ldrsw	x0, [x29, #180]
    2680:	d37df000 	lsl	x0, x0, #3
    2684:	910263a1 	add	x1, x29, #0x98
    2688:	f8606822 	ldr	x2, [x1, x0]
    268c:	b940e7a1 	ldr	w1, [x29, #228]
    2690:	b940c3a0 	ldr	w0, [x29, #192]
    2694:	0b000020 	add	w0, w1, w0
    2698:	93407c01 	sxtw	x1, w0
    269c:	aa0103e0 	mov	x0, x1
    26a0:	d37ff800 	lsl	x0, x0, #1
    26a4:	8b010000 	add	x0, x0, x1
    26a8:	d37ff800 	lsl	x0, x0, #1
    26ac:	8b000040 	add	x0, x2, x0
    26b0:	79c00400 	ldrsh	w0, [x0, #2]
    26b4:	12003c01 	and	w1, w0, #0xffff
    26b8:	b940cba2 	ldr	w2, [x29, #200]
    26bc:	b940bba0 	ldr	w0, [x29, #184]
    26c0:	1b007c40 	mul	w0, w2, w0
    26c4:	13067c00 	asr	w0, w0, #6
    26c8:	12003c00 	and	w0, w0, #0xffff
    26cc:	0b000020 	add	w0, w1, w0
    26d0:	12003c03 	and	w3, w0, #0xffff
    26d4:	b980b7a0 	ldrsw	x0, [x29, #180]
    26d8:	d37df000 	lsl	x0, x0, #3
    26dc:	910263a1 	add	x1, x29, #0x98
    26e0:	f8606822 	ldr	x2, [x1, x0]
    26e4:	b940e7a1 	ldr	w1, [x29, #228]
    26e8:	b940c3a0 	ldr	w0, [x29, #192]
    26ec:	0b000020 	add	w0, w1, w0
    26f0:	93407c01 	sxtw	x1, w0
    26f4:	aa0103e0 	mov	x0, x1
    26f8:	d37ff800 	lsl	x0, x0, #1
    26fc:	8b010000 	add	x0, x0, x1
    2700:	d37ff800 	lsl	x0, x0, #1
    2704:	8b000040 	add	x0, x2, x0
    2708:	13003c61 	sxth	w1, w3
    270c:	79000401 	strh	w1, [x0, #2]
    2710:	b980b7a0 	ldrsw	x0, [x29, #180]
    2714:	d37df000 	lsl	x0, x0, #3
    2718:	910263a1 	add	x1, x29, #0x98
    271c:	f8606822 	ldr	x2, [x1, x0]
    2720:	b940e7a1 	ldr	w1, [x29, #228]
    2724:	b940c3a0 	ldr	w0, [x29, #192]
    2728:	0b000020 	add	w0, w1, w0
    272c:	93407c01 	sxtw	x1, w0
    2730:	aa0103e0 	mov	x0, x1
    2734:	d37ff800 	lsl	x0, x0, #1
    2738:	8b010000 	add	x0, x0, x1
    273c:	d37ff800 	lsl	x0, x0, #1
    2740:	8b000040 	add	x0, x2, x0
    2744:	79c00800 	ldrsh	w0, [x0, #4]
    2748:	12003c01 	and	w1, w0, #0xffff
    274c:	b940c7a2 	ldr	w2, [x29, #196]
    2750:	b940bba0 	ldr	w0, [x29, #184]
    2754:	1b007c40 	mul	w0, w2, w0
    2758:	13067c00 	asr	w0, w0, #6
    275c:	12003c00 	and	w0, w0, #0xffff
    2760:	0b000020 	add	w0, w1, w0
    2764:	12003c03 	and	w3, w0, #0xffff
    2768:	b980b7a0 	ldrsw	x0, [x29, #180]
    276c:	d37df000 	lsl	x0, x0, #3
    2770:	910263a1 	add	x1, x29, #0x98
    2774:	f8606822 	ldr	x2, [x1, x0]
    2778:	b940e7a1 	ldr	w1, [x29, #228]
    277c:	b940c3a0 	ldr	w0, [x29, #192]
    2780:	0b000020 	add	w0, w1, w0
    2784:	93407c01 	sxtw	x1, w0
    2788:	aa0103e0 	mov	x0, x1
    278c:	d37ff800 	lsl	x0, x0, #1
    2790:	8b010000 	add	x0, x0, x1
    2794:	d37ff800 	lsl	x0, x0, #1
    2798:	8b000040 	add	x0, x2, x0
    279c:	13003c61 	sxth	w1, w3
    27a0:	79000801 	strh	w1, [x0, #4]
    27a4:	14000004 	b	27b4 <ImageProcess_Dither_AIO+0x670>
    27a8:	d503201f 	nop
    27ac:	14000002 	b	27b4 <ImageProcess_Dither_AIO+0x670>
    27b0:	d503201f 	nop
    27b4:	b940e3a0 	ldr	w0, [x29, #224]
    27b8:	11000400 	add	w0, w0, #0x1
    27bc:	b900e3a0 	str	w0, [x29, #224]
    27c0:	b940e3a0 	ldr	w0, [x29, #224]
    27c4:	71002c1f 	cmp	w0, #0xb
    27c8:	54ffeaed 	b.le	2524 <ImageProcess_Dither_AIO+0x3e0>
    27cc:	b940e7a0 	ldr	w0, [x29, #228]
    27d0:	11000400 	add	w0, w0, #0x1
    27d4:	b900e7a0 	str	w0, [x29, #228]
    27d8:	b940e7a1 	ldr	w1, [x29, #228]
    27dc:	b94017a0 	ldr	w0, [x29, #20]
    27e0:	6b00003f 	cmp	w1, w0
    27e4:	54ffd54b 	b.lt	228c <ImageProcess_Dither_AIO+0x148>  // b.tstop
    27e8:	f9404fa3 	ldr	x3, [x29, #152]
    27ec:	f94053a4 	ldr	x4, [x29, #160]
    27f0:	b98017a1 	ldrsw	x1, [x29, #20]
    27f4:	aa0103e0 	mov	x0, x1
    27f8:	d37ff800 	lsl	x0, x0, #1
    27fc:	8b010000 	add	x0, x0, x1
    2800:	d37ff800 	lsl	x0, x0, #1
    2804:	aa0003e2 	mov	x2, x0
    2808:	aa0403e1 	mov	x1, x4
    280c:	aa0303e0 	mov	x0, x3
    2810:	97fff920 	bl	c90 <memcpy@plt>
    2814:	f94053a3 	ldr	x3, [x29, #160]
    2818:	f94057a4 	ldr	x4, [x29, #168]
    281c:	b98017a1 	ldrsw	x1, [x29, #20]
    2820:	aa0103e0 	mov	x0, x1
    2824:	d37ff800 	lsl	x0, x0, #1
    2828:	8b010000 	add	x0, x0, x1
    282c:	d37ff800 	lsl	x0, x0, #1
    2830:	aa0003e2 	mov	x2, x0
    2834:	aa0403e1 	mov	x1, x4
    2838:	aa0303e0 	mov	x0, x3
    283c:	97fff915 	bl	c90 <memcpy@plt>
    2840:	f94057a3 	ldr	x3, [x29, #168]
    2844:	b98017a1 	ldrsw	x1, [x29, #20]
    2848:	aa0103e0 	mov	x0, x1
    284c:	d37ff800 	lsl	x0, x0, #1
    2850:	8b010000 	add	x0, x0, x1
    2854:	d37ff800 	lsl	x0, x0, #1
    2858:	aa0003e2 	mov	x2, x0
    285c:	52800001 	mov	w1, #0x0                   	// #0
    2860:	aa0303e0 	mov	x0, x3
    2864:	97fff91f 	bl	ce0 <memset@plt>
    2868:	b940eba0 	ldr	w0, [x29, #232]
    286c:	11000400 	add	w0, w0, #0x1
    2870:	b900eba0 	str	w0, [x29, #232]
    2874:	b940eba1 	ldr	w1, [x29, #232]
    2878:	b94013a0 	ldr	w0, [x29, #16]
    287c:	6b00003f 	cmp	w1, w0
    2880:	54ffd02b 	b.lt	2284 <ImageProcess_Dither_AIO+0x140>  // b.tstop
    2884:	f9404fa0 	ldr	x0, [x29, #152]
    2888:	97fff92a 	bl	d30 <free@plt>
    288c:	f94053a0 	ldr	x0, [x29, #160]
    2890:	97fff928 	bl	d30 <free@plt>
    2894:	f94057a0 	ldr	x0, [x29, #168]
    2898:	97fff926 	bl	d30 <free@plt>
    289c:	a8cf7bfd 	ldp	x29, x30, [sp], #240
    28a0:	d65f03c0 	ret

00000000000028a4 <ImageProcess_Spectra6_AIO>:
    28a4:	a9bd7bfd 	stp	x29, x30, [sp, #-48]!
    28a8:	910003fd 	mov	x29, sp
    28ac:	bd002fa0 	str	s0, [x29, #44]
    28b0:	bd002ba1 	str	s1, [x29, #40]
    28b4:	bd0027a2 	str	s2, [x29, #36]
    28b8:	bd0023a3 	str	s3, [x29, #32]
    28bc:	f9000fa0 	str	x0, [x29, #24]
    28c0:	b90017a1 	str	w1, [x29, #20]
    28c4:	b90013a2 	str	w2, [x29, #16]
    28c8:	b94013a2 	ldr	w2, [x29, #16]
    28cc:	b94017a1 	ldr	w1, [x29, #20]
    28d0:	f9400fa0 	ldr	x0, [x29, #24]
    28d4:	bd4023a3 	ldr	s3, [x29, #32]
    28d8:	bd4027a2 	ldr	s2, [x29, #36]
    28dc:	bd402ba1 	ldr	s1, [x29, #40]
    28e0:	bd402fa0 	ldr	s0, [x29, #44]
    28e4:	97fff8ef 	bl	ca0 <ImageProcess_ColorEnhace@plt>
    28e8:	b94013a2 	ldr	w2, [x29, #16]
    28ec:	b94017a1 	ldr	w1, [x29, #20]
    28f0:	f9400fa0 	ldr	x0, [x29, #24]
    28f4:	97fffe14 	bl	2144 <ImageProcess_Dither_AIO>
    28f8:	d503201f 	nop
    28fc:	a8c37bfd 	ldp	x29, x30, [sp], #48
    2900:	d65f03c0 	ret

0000000000002904 <IndexMapping_Spectra6_AIO_Y4>:
    2904:	a9ba7bfd 	stp	x29, x30, [sp, #-96]!
    2908:	910003fd 	mov	x29, sp
    290c:	f90017a0 	str	x0, [x29, #40]
    2910:	f90013a1 	str	x1, [x29, #32]
    2914:	b9001fa2 	str	w2, [x29, #28]
    2918:	b9001ba3 	str	w3, [x29, #24]
    291c:	1e2f1000 	fmov	s0, #1.500000000000000000e+00
    2920:	bd0053a0 	str	s0, [x29, #80]
    2924:	529999a0 	mov	w0, #0xcccd                	// #52429
    2928:	72a7a980 	movk	w0, #0x3d4c, lsl #16
    292c:	1e270000 	fmov	s0, w0
    2930:	bd004fa0 	str	s0, [x29, #76]
    2934:	1e249000 	fmov	s0, #1.000000000000000000e+01
    2938:	bd004ba0 	str	s0, [x29, #72]
    293c:	b90047bf 	str	wzr, [x29, #68]
    2940:	b9401ba2 	ldr	w2, [x29, #24]
    2944:	b9401fa1 	ldr	w1, [x29, #28]
    2948:	f94017a0 	ldr	x0, [x29, #40]
    294c:	bd4047a3 	ldr	s3, [x29, #68]
    2950:	bd404ba2 	ldr	s2, [x29, #72]
    2954:	bd404fa1 	ldr	s1, [x29, #76]
    2958:	bd4053a0 	ldr	s0, [x29, #80]
    295c:	97ffffd2 	bl	28a4 <ImageProcess_Spectra6_AIO>
    2960:	b9005fbf 	str	wzr, [x29, #92]
    2964:	b9005bbf 	str	wzr, [x29, #88]
    2968:	1400006a 	b	2b10 <IndexMapping_Spectra6_AIO_Y4+0x20c>
    296c:	b9805fa0 	ldrsw	x0, [x29, #92]
    2970:	f94017a1 	ldr	x1, [x29, #40]
    2974:	8b000020 	add	x0, x1, x0
    2978:	39400000 	ldrb	w0, [x0]
    297c:	39010fa0 	strb	w0, [x29, #67]
    2980:	b9805fa0 	ldrsw	x0, [x29, #92]
    2984:	91000400 	add	x0, x0, #0x1
    2988:	f94017a1 	ldr	x1, [x29, #40]
    298c:	8b000020 	add	x0, x1, x0
    2990:	39400000 	ldrb	w0, [x0]
    2994:	39010ba0 	strb	w0, [x29, #66]
    2998:	b9805fa0 	ldrsw	x0, [x29, #92]
    299c:	91000800 	add	x0, x0, #0x2
    29a0:	f94017a1 	ldr	x1, [x29, #40]
    29a4:	8b000020 	add	x0, x1, x0
    29a8:	39400000 	ldrb	w0, [x0]
    29ac:	390107a0 	strb	w0, [x29, #65]
    29b0:	b90057bf 	str	wzr, [x29, #84]
    29b4:	1400004e 	b	2aec <IndexMapping_Spectra6_AIO_Y4+0x1e8>
    29b8:	b0000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    29bc:	f947e402 	ldr	x2, [x0, #4040]
    29c0:	b98057a1 	ldrsw	x1, [x29, #84]
    29c4:	aa0103e0 	mov	x0, x1
    29c8:	d37ff800 	lsl	x0, x0, #1
    29cc:	8b010000 	add	x0, x0, x1
    29d0:	8b000040 	add	x0, x2, x0
    29d4:	39400000 	ldrb	w0, [x0]
    29d8:	39410fa1 	ldrb	w1, [x29, #67]
    29dc:	6b00003f 	cmp	w1, w0
    29e0:	54000801 	b.ne	2ae0 <IndexMapping_Spectra6_AIO_Y4+0x1dc>  // b.any
    29e4:	b0000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    29e8:	f947e402 	ldr	x2, [x0, #4040]
    29ec:	b98057a1 	ldrsw	x1, [x29, #84]
    29f0:	aa0103e0 	mov	x0, x1
    29f4:	d37ff800 	lsl	x0, x0, #1
    29f8:	8b010000 	add	x0, x0, x1
    29fc:	8b000040 	add	x0, x2, x0
    2a00:	39400400 	ldrb	w0, [x0, #1]
    2a04:	39410ba1 	ldrb	w1, [x29, #66]
    2a08:	6b00003f 	cmp	w1, w0
    2a0c:	540006a1 	b.ne	2ae0 <IndexMapping_Spectra6_AIO_Y4+0x1dc>  // b.any
    2a10:	b0000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    2a14:	f947e402 	ldr	x2, [x0, #4040]
    2a18:	b98057a1 	ldrsw	x1, [x29, #84]
    2a1c:	aa0103e0 	mov	x0, x1
    2a20:	d37ff800 	lsl	x0, x0, #1
    2a24:	8b010000 	add	x0, x0, x1
    2a28:	8b000040 	add	x0, x2, x0
    2a2c:	39400800 	ldrb	w0, [x0, #2]
    2a30:	394107a1 	ldrb	w1, [x29, #65]
    2a34:	6b00003f 	cmp	w1, w0
    2a38:	54000541 	b.ne	2ae0 <IndexMapping_Spectra6_AIO_Y4+0x1dc>  // b.any
    2a3c:	b0000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    2a40:	f947e801 	ldr	x1, [x0, #4048]
    2a44:	b98057a0 	ldrsw	x0, [x29, #84]
    2a48:	38606820 	ldrb	w0, [x1, x0]
    2a4c:	b9003fa0 	str	w0, [x29, #60]
    2a50:	b9405ba0 	ldr	w0, [x29, #88]
    2a54:	12000000 	and	w0, w0, #0x1
    2a58:	7100001f 	cmp	w0, #0x0
    2a5c:	540001a1 	b.ne	2a90 <IndexMapping_Spectra6_AIO_Y4+0x18c>  // b.any
    2a60:	b9403fa0 	ldr	w0, [x29, #60]
    2a64:	531c6c02 	lsl	w2, w0, #4
    2a68:	b9405ba0 	ldr	w0, [x29, #88]
    2a6c:	531f7c01 	lsr	w1, w0, #31
    2a70:	0b000020 	add	w0, w1, w0
    2a74:	13017c00 	asr	w0, w0, #1
    2a78:	93407c00 	sxtw	x0, w0
    2a7c:	f94013a1 	ldr	x1, [x29, #32]
    2a80:	8b000020 	add	x0, x1, x0
    2a84:	12001c41 	and	w1, w2, #0xff
    2a88:	39000001 	strb	w1, [x0]
    2a8c:	1400001b 	b	2af8 <IndexMapping_Spectra6_AIO_Y4+0x1f4>
    2a90:	b9405ba0 	ldr	w0, [x29, #88]
    2a94:	531f7c01 	lsr	w1, w0, #31
    2a98:	0b000020 	add	w0, w1, w0
    2a9c:	13017c00 	asr	w0, w0, #1
    2aa0:	2a0003e3 	mov	w3, w0
    2aa4:	93407c60 	sxtw	x0, w3
    2aa8:	f94013a1 	ldr	x1, [x29, #32]
    2aac:	8b000020 	add	x0, x1, x0
    2ab0:	39400002 	ldrb	w2, [x0]
    2ab4:	b9403fa0 	ldr	w0, [x29, #60]
    2ab8:	12001c00 	and	w0, w0, #0xff
    2abc:	12000c00 	and	w0, w0, #0xf
    2ac0:	12001c01 	and	w1, w0, #0xff
    2ac4:	93407c60 	sxtw	x0, w3
    2ac8:	f94013a3 	ldr	x3, [x29, #32]
    2acc:	8b000060 	add	x0, x3, x0
    2ad0:	0b010041 	add	w1, w2, w1
    2ad4:	12001c21 	and	w1, w1, #0xff
    2ad8:	39000001 	strb	w1, [x0]
    2adc:	14000007 	b	2af8 <IndexMapping_Spectra6_AIO_Y4+0x1f4>
    2ae0:	b94057a0 	ldr	w0, [x29, #84]
    2ae4:	11000400 	add	w0, w0, #0x1
    2ae8:	b90057a0 	str	w0, [x29, #84]
    2aec:	b94057a0 	ldr	w0, [x29, #84]
    2af0:	7100141f 	cmp	w0, #0x5
    2af4:	54fff62d 	b.le	29b8 <IndexMapping_Spectra6_AIO_Y4+0xb4>
    2af8:	b9405fa0 	ldr	w0, [x29, #92]
    2afc:	11000c00 	add	w0, w0, #0x3
    2b00:	b9005fa0 	str	w0, [x29, #92]
    2b04:	b9405ba0 	ldr	w0, [x29, #88]
    2b08:	11000400 	add	w0, w0, #0x1
    2b0c:	b9005ba0 	str	w0, [x29, #88]
    2b10:	b9401fa1 	ldr	w1, [x29, #28]
    2b14:	b9401ba0 	ldr	w0, [x29, #24]
    2b18:	1b007c20 	mul	w0, w1, w0
    2b1c:	b9405ba1 	ldr	w1, [x29, #88]
    2b20:	6b00003f 	cmp	w1, w0
    2b24:	54fff24b 	b.lt	296c <IndexMapping_Spectra6_AIO_Y4+0x68>  // b.tstop
    2b28:	d503201f 	nop
    2b2c:	a8c67bfd 	ldp	x29, x30, [sp], #96
    2b30:	d65f03c0 	ret

0000000000002b34 <IndexMapping_Spectra6_AIO_Y8>:
    2b34:	a9bb7bfd 	stp	x29, x30, [sp, #-80]!
    2b38:	910003fd 	mov	x29, sp
    2b3c:	f90017a0 	str	x0, [x29, #40]
    2b40:	f90013a1 	str	x1, [x29, #32]
    2b44:	b9001fa2 	str	w2, [x29, #28]
    2b48:	b9001ba3 	str	w3, [x29, #24]
    2b4c:	1e2f1000 	fmov	s0, #1.500000000000000000e+00
    2b50:	bd0043a0 	str	s0, [x29, #64]
    2b54:	b9003fbf 	str	wzr, [x29, #60]
    2b58:	b9003bbf 	str	wzr, [x29, #56]
    2b5c:	b90037bf 	str	wzr, [x29, #52]
    2b60:	b9401ba2 	ldr	w2, [x29, #24]
    2b64:	b9401fa1 	ldr	w1, [x29, #28]
    2b68:	f94017a0 	ldr	x0, [x29, #40]
    2b6c:	bd4037a3 	ldr	s3, [x29, #52]
    2b70:	bd403ba2 	ldr	s2, [x29, #56]
    2b74:	bd403fa1 	ldr	s1, [x29, #60]
    2b78:	bd4043a0 	ldr	s0, [x29, #64]
    2b7c:	97ffff4a 	bl	28a4 <ImageProcess_Spectra6_AIO>
    2b80:	b9004fbf 	str	wzr, [x29, #76]
    2b84:	b9004bbf 	str	wzr, [x29, #72]
    2b88:	14000048 	b	2ca8 <IndexMapping_Spectra6_AIO_Y8+0x174>
    2b8c:	b9804fa0 	ldrsw	x0, [x29, #76]
    2b90:	f94017a1 	ldr	x1, [x29, #40]
    2b94:	8b000020 	add	x0, x1, x0
    2b98:	39400000 	ldrb	w0, [x0]
    2b9c:	3900cfa0 	strb	w0, [x29, #51]
    2ba0:	b9804fa0 	ldrsw	x0, [x29, #76]
    2ba4:	91000400 	add	x0, x0, #0x1
    2ba8:	f94017a1 	ldr	x1, [x29, #40]
    2bac:	8b000020 	add	x0, x1, x0
    2bb0:	39400000 	ldrb	w0, [x0]
    2bb4:	3900cba0 	strb	w0, [x29, #50]
    2bb8:	b9804fa0 	ldrsw	x0, [x29, #76]
    2bbc:	91000800 	add	x0, x0, #0x2
    2bc0:	f94017a1 	ldr	x1, [x29, #40]
    2bc4:	8b000020 	add	x0, x1, x0
    2bc8:	39400000 	ldrb	w0, [x0]
    2bcc:	3900c7a0 	strb	w0, [x29, #49]
    2bd0:	b90047bf 	str	wzr, [x29, #68]
    2bd4:	1400002c 	b	2c84 <IndexMapping_Spectra6_AIO_Y8+0x150>
    2bd8:	b0000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    2bdc:	f947e402 	ldr	x2, [x0, #4040]
    2be0:	b98047a1 	ldrsw	x1, [x29, #68]
    2be4:	aa0103e0 	mov	x0, x1
    2be8:	d37ff800 	lsl	x0, x0, #1
    2bec:	8b010000 	add	x0, x0, x1
    2bf0:	8b000040 	add	x0, x2, x0
    2bf4:	39400000 	ldrb	w0, [x0]
    2bf8:	3940cfa1 	ldrb	w1, [x29, #51]
    2bfc:	6b00003f 	cmp	w1, w0
    2c00:	540003c1 	b.ne	2c78 <IndexMapping_Spectra6_AIO_Y8+0x144>  // b.any
    2c04:	b0000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    2c08:	f947e402 	ldr	x2, [x0, #4040]
    2c0c:	b98047a1 	ldrsw	x1, [x29, #68]
    2c10:	aa0103e0 	mov	x0, x1
    2c14:	d37ff800 	lsl	x0, x0, #1
    2c18:	8b010000 	add	x0, x0, x1
    2c1c:	8b000040 	add	x0, x2, x0
    2c20:	39400400 	ldrb	w0, [x0, #1]
    2c24:	3940cba1 	ldrb	w1, [x29, #50]
    2c28:	6b00003f 	cmp	w1, w0
    2c2c:	54000261 	b.ne	2c78 <IndexMapping_Spectra6_AIO_Y8+0x144>  // b.any
    2c30:	b0000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    2c34:	f947e402 	ldr	x2, [x0, #4040]
    2c38:	b98047a1 	ldrsw	x1, [x29, #68]
    2c3c:	aa0103e0 	mov	x0, x1
    2c40:	d37ff800 	lsl	x0, x0, #1
    2c44:	8b010000 	add	x0, x0, x1
    2c48:	8b000040 	add	x0, x2, x0
    2c4c:	39400800 	ldrb	w0, [x0, #2]
    2c50:	3940c7a1 	ldrb	w1, [x29, #49]
    2c54:	6b00003f 	cmp	w1, w0
    2c58:	54000101 	b.ne	2c78 <IndexMapping_Spectra6_AIO_Y8+0x144>  // b.any
    2c5c:	b9804ba0 	ldrsw	x0, [x29, #72]
    2c60:	f94013a1 	ldr	x1, [x29, #32]
    2c64:	8b000020 	add	x0, x1, x0
    2c68:	b94047a1 	ldr	w1, [x29, #68]
    2c6c:	12001c21 	and	w1, w1, #0xff
    2c70:	39000001 	strb	w1, [x0]
    2c74:	14000007 	b	2c90 <IndexMapping_Spectra6_AIO_Y8+0x15c>
    2c78:	b94047a0 	ldr	w0, [x29, #68]
    2c7c:	11000400 	add	w0, w0, #0x1
    2c80:	b90047a0 	str	w0, [x29, #68]
    2c84:	b94047a0 	ldr	w0, [x29, #68]
    2c88:	7100141f 	cmp	w0, #0x5
    2c8c:	54fffa6d 	b.le	2bd8 <IndexMapping_Spectra6_AIO_Y8+0xa4>
    2c90:	b9404fa0 	ldr	w0, [x29, #76]
    2c94:	11000c00 	add	w0, w0, #0x3
    2c98:	b9004fa0 	str	w0, [x29, #76]
    2c9c:	b9404ba0 	ldr	w0, [x29, #72]
    2ca0:	11000400 	add	w0, w0, #0x1
    2ca4:	b9004ba0 	str	w0, [x29, #72]
    2ca8:	b9401fa1 	ldr	w1, [x29, #28]
    2cac:	b9401ba0 	ldr	w0, [x29, #24]
    2cb0:	1b007c20 	mul	w0, w1, w0
    2cb4:	b9404ba1 	ldr	w1, [x29, #72]
    2cb8:	6b00003f 	cmp	w1, w0
    2cbc:	54fff68b 	b.lt	2b8c <IndexMapping_Spectra6_AIO_Y8+0x58>  // b.tstop
    2cc0:	d503201f 	nop
    2cc4:	a8c57bfd 	ldp	x29, x30, [sp], #80
    2cc8:	d65f03c0 	ret

0000000000002ccc <IndexMapping_Spectra6_T2000_Y8>:
    2ccc:	a9bc7bfd 	stp	x29, x30, [sp, #-64]!
    2cd0:	910003fd 	mov	x29, sp
    2cd4:	f90017a0 	str	x0, [x29, #40]
    2cd8:	f90013a1 	str	x1, [x29, #32]
    2cdc:	b9001fa2 	str	w2, [x29, #28]
    2ce0:	b9001ba3 	str	w3, [x29, #24]
    2ce4:	b9401ba2 	ldr	w2, [x29, #24]
    2ce8:	b9401fa1 	ldr	w1, [x29, #28]
    2cec:	f94017a0 	ldr	x0, [x29, #40]
    2cf0:	0f000403 	movi	v3.2s, #0x0
    2cf4:	52a84183 	mov	w3, #0x420c0000            	// #1108082688
    2cf8:	1e270062 	fmov	s2, w3
    2cfc:	529999a3 	mov	w3, #0xcccd                	// #52429
    2d00:	72a7b983 	movk	w3, #0x3dcc, lsl #16
    2d04:	1e270061 	fmov	s1, w3
    2d08:	1e2f1000 	fmov	s0, #1.500000000000000000e+00
    2d0c:	97fffee6 	bl	28a4 <ImageProcess_Spectra6_AIO>
    2d10:	b9003fbf 	str	wzr, [x29, #60]
    2d14:	b9003bbf 	str	wzr, [x29, #56]
    2d18:	1400004a 	b	2e40 <IndexMapping_Spectra6_T2000_Y8+0x174>
    2d1c:	b9803fa0 	ldrsw	x0, [x29, #60]
    2d20:	f94017a1 	ldr	x1, [x29, #40]
    2d24:	8b000020 	add	x0, x1, x0
    2d28:	39400000 	ldrb	w0, [x0]
    2d2c:	3900cfa0 	strb	w0, [x29, #51]
    2d30:	b9803fa0 	ldrsw	x0, [x29, #60]
    2d34:	91000400 	add	x0, x0, #0x1
    2d38:	f94017a1 	ldr	x1, [x29, #40]
    2d3c:	8b000020 	add	x0, x1, x0
    2d40:	39400000 	ldrb	w0, [x0]
    2d44:	3900cba0 	strb	w0, [x29, #50]
    2d48:	b9803fa0 	ldrsw	x0, [x29, #60]
    2d4c:	91000800 	add	x0, x0, #0x2
    2d50:	f94017a1 	ldr	x1, [x29, #40]
    2d54:	8b000020 	add	x0, x1, x0
    2d58:	39400000 	ldrb	w0, [x0]
    2d5c:	3900c7a0 	strb	w0, [x29, #49]
    2d60:	b90037bf 	str	wzr, [x29, #52]
    2d64:	1400002e 	b	2e1c <IndexMapping_Spectra6_T2000_Y8+0x150>
    2d68:	b0000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    2d6c:	f947e402 	ldr	x2, [x0, #4040]
    2d70:	b98037a1 	ldrsw	x1, [x29, #52]
    2d74:	aa0103e0 	mov	x0, x1
    2d78:	d37ff800 	lsl	x0, x0, #1
    2d7c:	8b010000 	add	x0, x0, x1
    2d80:	8b000040 	add	x0, x2, x0
    2d84:	39400000 	ldrb	w0, [x0]
    2d88:	3940cfa1 	ldrb	w1, [x29, #51]
    2d8c:	6b00003f 	cmp	w1, w0
    2d90:	54000401 	b.ne	2e10 <IndexMapping_Spectra6_T2000_Y8+0x144>  // b.any
    2d94:	b0000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    2d98:	f947e402 	ldr	x2, [x0, #4040]
    2d9c:	b98037a1 	ldrsw	x1, [x29, #52]
    2da0:	aa0103e0 	mov	x0, x1
    2da4:	d37ff800 	lsl	x0, x0, #1
    2da8:	8b010000 	add	x0, x0, x1
    2dac:	8b000040 	add	x0, x2, x0
    2db0:	39400400 	ldrb	w0, [x0, #1]
    2db4:	3940cba1 	ldrb	w1, [x29, #50]
    2db8:	6b00003f 	cmp	w1, w0
    2dbc:	540002a1 	b.ne	2e10 <IndexMapping_Spectra6_T2000_Y8+0x144>  // b.any
    2dc0:	b0000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    2dc4:	f947e402 	ldr	x2, [x0, #4040]
    2dc8:	b98037a1 	ldrsw	x1, [x29, #52]
    2dcc:	aa0103e0 	mov	x0, x1
    2dd0:	d37ff800 	lsl	x0, x0, #1
    2dd4:	8b010000 	add	x0, x0, x1
    2dd8:	8b000040 	add	x0, x2, x0
    2ddc:	39400800 	ldrb	w0, [x0, #2]
    2de0:	3940c7a1 	ldrb	w1, [x29, #49]
    2de4:	6b00003f 	cmp	w1, w0
    2de8:	54000141 	b.ne	2e10 <IndexMapping_Spectra6_T2000_Y8+0x144>  // b.any
    2dec:	b9803ba0 	ldrsw	x0, [x29, #56]
    2df0:	f94013a1 	ldr	x1, [x29, #32]
    2df4:	8b000020 	add	x0, x1, x0
    2df8:	b0000081 	adrp	x1, 13000 <__FRAME_END__+0xf4b0>
    2dfc:	f947d422 	ldr	x2, [x1, #4008]
    2e00:	b98037a1 	ldrsw	x1, [x29, #52]
    2e04:	38616841 	ldrb	w1, [x2, x1]
    2e08:	39000001 	strb	w1, [x0]
    2e0c:	14000007 	b	2e28 <IndexMapping_Spectra6_T2000_Y8+0x15c>
    2e10:	b94037a0 	ldr	w0, [x29, #52]
    2e14:	11000400 	add	w0, w0, #0x1
    2e18:	b90037a0 	str	w0, [x29, #52]
    2e1c:	b94037a0 	ldr	w0, [x29, #52]
    2e20:	7100141f 	cmp	w0, #0x5
    2e24:	54fffa2d 	b.le	2d68 <IndexMapping_Spectra6_T2000_Y8+0x9c>
    2e28:	b9403fa0 	ldr	w0, [x29, #60]
    2e2c:	11000c00 	add	w0, w0, #0x3
    2e30:	b9003fa0 	str	w0, [x29, #60]
    2e34:	b9403ba0 	ldr	w0, [x29, #56]
    2e38:	11000400 	add	w0, w0, #0x1
    2e3c:	b9003ba0 	str	w0, [x29, #56]
    2e40:	b9401fa1 	ldr	w1, [x29, #28]
    2e44:	b9401ba0 	ldr	w0, [x29, #24]
    2e48:	1b007c20 	mul	w0, w1, w0
    2e4c:	b9403ba1 	ldr	w1, [x29, #56]
    2e50:	6b00003f 	cmp	w1, w0
    2e54:	54fff64b 	b.lt	2d1c <IndexMapping_Spectra6_T2000_Y8+0x50>  // b.tstop
    2e58:	d503201f 	nop
    2e5c:	a8c47bfd 	ldp	x29, x30, [sp], #64
    2e60:	d65f03c0 	ret

0000000000002e64 <find_nearest_color_T2001>:
    2e64:	d100c3ff 	sub	sp, sp, #0x30
    2e68:	b9000fe0 	str	w0, [sp, #12]
    2e6c:	b9000be1 	str	w1, [sp, #8]
    2e70:	b90007e2 	str	w2, [sp, #4]
    2e74:	12b00000 	mov	w0, #0x7fffffff            	// #2147483647
    2e78:	b9002fe0 	str	w0, [sp, #44]
    2e7c:	b9002bff 	str	wzr, [sp, #40]
    2e80:	b90027ff 	str	wzr, [sp, #36]
    2e84:	1400003c 	b	2f74 <find_nearest_color_T2001+0x110>
    2e88:	b9400fe0 	ldr	w0, [sp, #12]
    2e8c:	12001c02 	and	w2, w0, #0xff
    2e90:	b0000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    2e94:	f947e003 	ldr	x3, [x0, #4032]
    2e98:	b98027e1 	ldrsw	x1, [sp, #36]
    2e9c:	aa0103e0 	mov	x0, x1
    2ea0:	d37ff800 	lsl	x0, x0, #1
    2ea4:	8b010000 	add	x0, x0, x1
    2ea8:	8b000060 	add	x0, x3, x0
    2eac:	39400000 	ldrb	w0, [x0]
    2eb0:	4b000040 	sub	w0, w2, w0
    2eb4:	b90023e0 	str	w0, [sp, #32]
    2eb8:	b9400be0 	ldr	w0, [sp, #8]
    2ebc:	12001c02 	and	w2, w0, #0xff
    2ec0:	b0000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    2ec4:	f947e003 	ldr	x3, [x0, #4032]
    2ec8:	b98027e1 	ldrsw	x1, [sp, #36]
    2ecc:	aa0103e0 	mov	x0, x1
    2ed0:	d37ff800 	lsl	x0, x0, #1
    2ed4:	8b010000 	add	x0, x0, x1
    2ed8:	8b000060 	add	x0, x3, x0
    2edc:	39400400 	ldrb	w0, [x0, #1]
    2ee0:	4b000040 	sub	w0, w2, w0
    2ee4:	b9001fe0 	str	w0, [sp, #28]
    2ee8:	b94007e0 	ldr	w0, [sp, #4]
    2eec:	12001c02 	and	w2, w0, #0xff
    2ef0:	b0000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    2ef4:	f947e003 	ldr	x3, [x0, #4032]
    2ef8:	b98027e1 	ldrsw	x1, [sp, #36]
    2efc:	aa0103e0 	mov	x0, x1
    2f00:	d37ff800 	lsl	x0, x0, #1
    2f04:	8b010000 	add	x0, x0, x1
    2f08:	8b000060 	add	x0, x3, x0
    2f0c:	39400800 	ldrb	w0, [x0, #2]
    2f10:	4b000040 	sub	w0, w2, w0
    2f14:	b9001be0 	str	w0, [sp, #24]
    2f18:	b94023e1 	ldr	w1, [sp, #32]
    2f1c:	b94023e0 	ldr	w0, [sp, #32]
    2f20:	1b007c21 	mul	w1, w1, w0
    2f24:	b9401fe2 	ldr	w2, [sp, #28]
    2f28:	b9401fe0 	ldr	w0, [sp, #28]
    2f2c:	1b007c40 	mul	w0, w2, w0
    2f30:	0b000021 	add	w1, w1, w0
    2f34:	b9401be2 	ldr	w2, [sp, #24]
    2f38:	b9401be0 	ldr	w0, [sp, #24]
    2f3c:	1b007c40 	mul	w0, w2, w0
    2f40:	0b000020 	add	w0, w1, w0
    2f44:	b90017e0 	str	w0, [sp, #20]
    2f48:	b94017e1 	ldr	w1, [sp, #20]
    2f4c:	b9402fe0 	ldr	w0, [sp, #44]
    2f50:	6b00003f 	cmp	w1, w0
    2f54:	540000aa 	b.ge	2f68 <find_nearest_color_T2001+0x104>  // b.tcont
    2f58:	b94017e0 	ldr	w0, [sp, #20]
    2f5c:	b9002fe0 	str	w0, [sp, #44]
    2f60:	b94027e0 	ldr	w0, [sp, #36]
    2f64:	b9002be0 	str	w0, [sp, #40]
    2f68:	b94027e0 	ldr	w0, [sp, #36]
    2f6c:	11000400 	add	w0, w0, #0x1
    2f70:	b90027e0 	str	w0, [sp, #36]
    2f74:	b94027e0 	ldr	w0, [sp, #36]
    2f78:	7100241f 	cmp	w0, #0x9
    2f7c:	54fff86d 	b.le	2e88 <find_nearest_color_T2001+0x24>
    2f80:	b9402be0 	ldr	w0, [sp, #40]
    2f84:	12001c00 	and	w0, w0, #0xff
    2f88:	9100c3ff 	add	sp, sp, #0x30
    2f8c:	d65f03c0 	ret

0000000000002f90 <ImageProcess_Dither_T2001>:
    2f90:	a9b17bfd 	stp	x29, x30, [sp, #-240]!
    2f94:	910003fd 	mov	x29, sp
    2f98:	f9000fa0 	str	x0, [x29, #24]
    2f9c:	b90017a1 	str	w1, [x29, #20]
    2fa0:	b90013a2 	str	w2, [x29, #16]
    2fa4:	b900efbf 	str	wzr, [x29, #236]
    2fa8:	b98017a1 	ldrsw	x1, [x29, #20]
    2fac:	aa0103e0 	mov	x0, x1
    2fb0:	d37ff800 	lsl	x0, x0, #1
    2fb4:	8b010000 	add	x0, x0, x1
    2fb8:	d37ff800 	lsl	x0, x0, #1
    2fbc:	97fff741 	bl	cc0 <malloc@plt>
    2fc0:	f9004fa0 	str	x0, [x29, #152]
    2fc4:	b98017a1 	ldrsw	x1, [x29, #20]
    2fc8:	aa0103e0 	mov	x0, x1
    2fcc:	d37ff800 	lsl	x0, x0, #1
    2fd0:	8b010000 	add	x0, x0, x1
    2fd4:	d37ff800 	lsl	x0, x0, #1
    2fd8:	97fff73a 	bl	cc0 <malloc@plt>
    2fdc:	f90053a0 	str	x0, [x29, #160]
    2fe0:	b98017a1 	ldrsw	x1, [x29, #20]
    2fe4:	aa0103e0 	mov	x0, x1
    2fe8:	d37ff800 	lsl	x0, x0, #1
    2fec:	8b010000 	add	x0, x0, x1
    2ff0:	d37ff800 	lsl	x0, x0, #1
    2ff4:	97fff733 	bl	cc0 <malloc@plt>
    2ff8:	f90057a0 	str	x0, [x29, #168]
    2ffc:	f9404fa3 	ldr	x3, [x29, #152]
    3000:	b98017a1 	ldrsw	x1, [x29, #20]
    3004:	aa0103e0 	mov	x0, x1
    3008:	d37ff800 	lsl	x0, x0, #1
    300c:	8b010000 	add	x0, x0, x1
    3010:	d37ff800 	lsl	x0, x0, #1
    3014:	aa0003e2 	mov	x2, x0
    3018:	52800001 	mov	w1, #0x0                   	// #0
    301c:	aa0303e0 	mov	x0, x3
    3020:	97fff730 	bl	ce0 <memset@plt>
    3024:	f94053a3 	ldr	x3, [x29, #160]
    3028:	b98017a1 	ldrsw	x1, [x29, #20]
    302c:	aa0103e0 	mov	x0, x1
    3030:	d37ff800 	lsl	x0, x0, #1
    3034:	8b010000 	add	x0, x0, x1
    3038:	d37ff800 	lsl	x0, x0, #1
    303c:	aa0003e2 	mov	x2, x0
    3040:	52800001 	mov	w1, #0x0                   	// #0
    3044:	aa0303e0 	mov	x0, x3
    3048:	97fff726 	bl	ce0 <memset@plt>
    304c:	f94057a3 	ldr	x3, [x29, #168]
    3050:	b98017a1 	ldrsw	x1, [x29, #20]
    3054:	aa0103e0 	mov	x0, x1
    3058:	d37ff800 	lsl	x0, x0, #1
    305c:	8b010000 	add	x0, x0, x1
    3060:	d37ff800 	lsl	x0, x0, #1
    3064:	aa0003e2 	mov	x2, x0
    3068:	52800001 	mov	w1, #0x0                   	// #0
    306c:	aa0303e0 	mov	x0, x3
    3070:	97fff71c 	bl	ce0 <memset@plt>
    3074:	f9404fa0 	ldr	x0, [x29, #152]
    3078:	f100001f 	cmp	x0, #0x0
    307c:	540000a1 	b.ne	3090 <ImageProcess_Dither_T2001+0x100>  // b.any
    3080:	90000000 	adrp	x0, 3000 <ImageProcess_Dither_T2001+0x70>
    3084:	912b0000 	add	x0, x0, #0xac0
    3088:	97fff726 	bl	d20 <puts@plt>
    308c:	14000197 	b	36e8 <ImageProcess_Dither_T2001+0x758>
    3090:	f94053a0 	ldr	x0, [x29, #160]
    3094:	f100001f 	cmp	x0, #0x0
    3098:	540000a1 	b.ne	30ac <ImageProcess_Dither_T2001+0x11c>  // b.any
    309c:	90000000 	adrp	x0, 3000 <ImageProcess_Dither_T2001+0x70>
    30a0:	912b0000 	add	x0, x0, #0xac0
    30a4:	97fff71f 	bl	d20 <puts@plt>
    30a8:	14000190 	b	36e8 <ImageProcess_Dither_T2001+0x758>
    30ac:	f94057a0 	ldr	x0, [x29, #168]
    30b0:	f100001f 	cmp	x0, #0x0
    30b4:	540000a1 	b.ne	30c8 <ImageProcess_Dither_T2001+0x138>  // b.any
    30b8:	90000000 	adrp	x0, 3000 <ImageProcess_Dither_T2001+0x70>
    30bc:	912b0000 	add	x0, x0, #0xac0
    30c0:	97fff718 	bl	d20 <puts@plt>
    30c4:	14000189 	b	36e8 <ImageProcess_Dither_T2001+0x758>
    30c8:	b900ebbf 	str	wzr, [x29, #232]
    30cc:	1400017d 	b	36c0 <ImageProcess_Dither_T2001+0x730>
    30d0:	b900e7bf 	str	wzr, [x29, #228]
    30d4:	14000154 	b	3624 <ImageProcess_Dither_T2001+0x694>
    30d8:	b980efa0 	ldrsw	x0, [x29, #236]
    30dc:	f9400fa1 	ldr	x1, [x29, #24]
    30e0:	8b000020 	add	x0, x1, x0
    30e4:	39400000 	ldrb	w0, [x0]
    30e8:	2a0003e3 	mov	w3, w0
    30ec:	f9404fa2 	ldr	x2, [x29, #152]
    30f0:	b980e7a1 	ldrsw	x1, [x29, #228]
    30f4:	aa0103e0 	mov	x0, x1
    30f8:	d37ff800 	lsl	x0, x0, #1
    30fc:	8b010000 	add	x0, x0, x1
    3100:	d37ff800 	lsl	x0, x0, #1
    3104:	8b000040 	add	x0, x2, x0
    3108:	79c00000 	ldrsh	w0, [x0]
    310c:	0b000060 	add	w0, w3, w0
    3110:	b900dfa0 	str	w0, [x29, #220]
    3114:	b980efa0 	ldrsw	x0, [x29, #236]
    3118:	91000400 	add	x0, x0, #0x1
    311c:	f9400fa1 	ldr	x1, [x29, #24]
    3120:	8b000020 	add	x0, x1, x0
    3124:	39400000 	ldrb	w0, [x0]
    3128:	2a0003e3 	mov	w3, w0
    312c:	f9404fa2 	ldr	x2, [x29, #152]
    3130:	b980e7a1 	ldrsw	x1, [x29, #228]
    3134:	aa0103e0 	mov	x0, x1
    3138:	d37ff800 	lsl	x0, x0, #1
    313c:	8b010000 	add	x0, x0, x1
    3140:	d37ff800 	lsl	x0, x0, #1
    3144:	8b000040 	add	x0, x2, x0
    3148:	79c00400 	ldrsh	w0, [x0, #2]
    314c:	0b000060 	add	w0, w3, w0
    3150:	b900dba0 	str	w0, [x29, #216]
    3154:	b980efa0 	ldrsw	x0, [x29, #236]
    3158:	91000800 	add	x0, x0, #0x2
    315c:	f9400fa1 	ldr	x1, [x29, #24]
    3160:	8b000020 	add	x0, x1, x0
    3164:	39400000 	ldrb	w0, [x0]
    3168:	2a0003e3 	mov	w3, w0
    316c:	f9404fa2 	ldr	x2, [x29, #152]
    3170:	b980e7a1 	ldrsw	x1, [x29, #228]
    3174:	aa0103e0 	mov	x0, x1
    3178:	d37ff800 	lsl	x0, x0, #1
    317c:	8b010000 	add	x0, x0, x1
    3180:	d37ff800 	lsl	x0, x0, #1
    3184:	8b000040 	add	x0, x2, x0
    3188:	79c00800 	ldrsh	w0, [x0, #4]
    318c:	0b000060 	add	w0, w3, w0
    3190:	b900d7a0 	str	w0, [x29, #212]
    3194:	b940dfa0 	ldr	w0, [x29, #220]
    3198:	1e220000 	scvtf	s0, w0
    319c:	0f000401 	movi	v1.2s, #0x0
    31a0:	97fff6dc 	bl	d10 <fmaxf@plt>
    31a4:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    31a8:	1e270001 	fmov	s1, w0
    31ac:	97fff6d1 	bl	cf0 <fminf@plt>
    31b0:	1e380000 	fcvtzs	w0, s0
    31b4:	b900dfa0 	str	w0, [x29, #220]
    31b8:	b940dba0 	ldr	w0, [x29, #216]
    31bc:	1e220000 	scvtf	s0, w0
    31c0:	0f000401 	movi	v1.2s, #0x0
    31c4:	97fff6d3 	bl	d10 <fmaxf@plt>
    31c8:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    31cc:	1e270001 	fmov	s1, w0
    31d0:	97fff6c8 	bl	cf0 <fminf@plt>
    31d4:	1e380000 	fcvtzs	w0, s0
    31d8:	b900dba0 	str	w0, [x29, #216]
    31dc:	b940d7a0 	ldr	w0, [x29, #212]
    31e0:	1e220000 	scvtf	s0, w0
    31e4:	0f000401 	movi	v1.2s, #0x0
    31e8:	97fff6ca 	bl	d10 <fmaxf@plt>
    31ec:	52a86fe0 	mov	w0, #0x437f0000            	// #1132396544
    31f0:	1e270001 	fmov	s1, w0
    31f4:	97fff6bf 	bl	cf0 <fminf@plt>
    31f8:	1e380000 	fcvtzs	w0, s0
    31fc:	b900d7a0 	str	w0, [x29, #212]
    3200:	b940d7a2 	ldr	w2, [x29, #212]
    3204:	b940dba1 	ldr	w1, [x29, #216]
    3208:	b940dfa0 	ldr	w0, [x29, #220]
    320c:	97ffff16 	bl	2e64 <find_nearest_color_T2001>
    3210:	39034fa0 	strb	w0, [x29, #211]
    3214:	39434fa1 	ldrb	w1, [x29, #211]
    3218:	90000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    321c:	f947e002 	ldr	x2, [x0, #4032]
    3220:	93407c21 	sxtw	x1, w1
    3224:	aa0103e0 	mov	x0, x1
    3228:	d37ff800 	lsl	x0, x0, #1
    322c:	8b010000 	add	x0, x0, x1
    3230:	8b000040 	add	x0, x2, x0
    3234:	39400000 	ldrb	w0, [x0]
    3238:	39034ba0 	strb	w0, [x29, #210]
    323c:	39434fa1 	ldrb	w1, [x29, #211]
    3240:	90000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    3244:	f947e002 	ldr	x2, [x0, #4032]
    3248:	93407c21 	sxtw	x1, w1
    324c:	aa0103e0 	mov	x0, x1
    3250:	d37ff800 	lsl	x0, x0, #1
    3254:	8b010000 	add	x0, x0, x1
    3258:	8b000040 	add	x0, x2, x0
    325c:	39400400 	ldrb	w0, [x0, #1]
    3260:	390347a0 	strb	w0, [x29, #209]
    3264:	39434fa1 	ldrb	w1, [x29, #211]
    3268:	90000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    326c:	f947e002 	ldr	x2, [x0, #4032]
    3270:	93407c21 	sxtw	x1, w1
    3274:	aa0103e0 	mov	x0, x1
    3278:	d37ff800 	lsl	x0, x0, #1
    327c:	8b010000 	add	x0, x0, x1
    3280:	8b000040 	add	x0, x2, x0
    3284:	39400800 	ldrb	w0, [x0, #2]
    3288:	390343a0 	strb	w0, [x29, #208]
    328c:	b980efa0 	ldrsw	x0, [x29, #236]
    3290:	f9400fa1 	ldr	x1, [x29, #24]
    3294:	8b000020 	add	x0, x1, x0
    3298:	39434ba1 	ldrb	w1, [x29, #210]
    329c:	39000001 	strb	w1, [x0]
    32a0:	b980efa0 	ldrsw	x0, [x29, #236]
    32a4:	91000400 	add	x0, x0, #0x1
    32a8:	f9400fa1 	ldr	x1, [x29, #24]
    32ac:	8b000020 	add	x0, x1, x0
    32b0:	394347a1 	ldrb	w1, [x29, #209]
    32b4:	39000001 	strb	w1, [x0]
    32b8:	b980efa0 	ldrsw	x0, [x29, #236]
    32bc:	91000800 	add	x0, x0, #0x2
    32c0:	f9400fa1 	ldr	x1, [x29, #24]
    32c4:	8b000020 	add	x0, x1, x0
    32c8:	394343a1 	ldrb	w1, [x29, #208]
    32cc:	39000001 	strb	w1, [x0]
    32d0:	b940efa0 	ldr	w0, [x29, #236]
    32d4:	11000c00 	add	w0, w0, #0x3
    32d8:	b900efa0 	str	w0, [x29, #236]
    32dc:	39434ba0 	ldrb	w0, [x29, #210]
    32e0:	b940dfa1 	ldr	w1, [x29, #220]
    32e4:	4b000020 	sub	w0, w1, w0
    32e8:	b900cfa0 	str	w0, [x29, #204]
    32ec:	394347a0 	ldrb	w0, [x29, #209]
    32f0:	b940dba1 	ldr	w1, [x29, #216]
    32f4:	4b000020 	sub	w0, w1, w0
    32f8:	b900cba0 	str	w0, [x29, #200]
    32fc:	394343a0 	ldrb	w0, [x29, #208]
    3300:	b940d7a1 	ldr	w1, [x29, #212]
    3304:	4b000020 	sub	w0, w1, w0
    3308:	b900c7a0 	str	w0, [x29, #196]
    330c:	90000000 	adrp	x0, 3000 <ImageProcess_Dither_T2001+0x70>
    3310:	912b6000 	add	x0, x0, #0xad8
    3314:	910203a2 	add	x2, x29, #0x80
    3318:	aa0003e3 	mov	x3, x0
    331c:	a9400460 	ldp	x0, x1, [x3]
    3320:	a9000440 	stp	x0, x1, [x2]
    3324:	f9400860 	ldr	x0, [x3, #16]
    3328:	f9000840 	str	x0, [x2, #16]
    332c:	90000000 	adrp	x0, 3000 <ImageProcess_Dither_T2001+0x70>
    3330:	912bc001 	add	x1, x0, #0xaf0
    3334:	910083a0 	add	x0, x29, #0x20
    3338:	a9400c22 	ldp	x2, x3, [x1]
    333c:	a9000c02 	stp	x2, x3, [x0]
    3340:	a9410c22 	ldp	x2, x3, [x1, #16]
    3344:	a9010c02 	stp	x2, x3, [x0, #16]
    3348:	a9420c22 	ldp	x2, x3, [x1, #32]
    334c:	a9020c02 	stp	x2, x3, [x0, #32]
    3350:	a9430c22 	ldp	x2, x3, [x1, #48]
    3354:	a9030c02 	stp	x2, x3, [x0, #48]
    3358:	a9440c22 	ldp	x2, x3, [x1, #64]
    335c:	a9040c02 	stp	x2, x3, [x0, #64]
    3360:	a9450821 	ldp	x1, x2, [x1, #80]
    3364:	a9050801 	stp	x1, x2, [x0, #80]
    3368:	b900e3bf 	str	wzr, [x29, #224]
    336c:	140000a8 	b	360c <ImageProcess_Dither_T2001+0x67c>
    3370:	b940e3a0 	ldr	w0, [x29, #224]
    3374:	531f7800 	lsl	w0, w0, #1
    3378:	93407c00 	sxtw	x0, w0
    337c:	d37ef400 	lsl	x0, x0, #2
    3380:	910083a1 	add	x1, x29, #0x20
    3384:	b8606820 	ldr	w0, [x1, x0]
    3388:	b900c3a0 	str	w0, [x29, #192]
    338c:	b940e3a0 	ldr	w0, [x29, #224]
    3390:	531f7800 	lsl	w0, w0, #1
    3394:	11000400 	add	w0, w0, #0x1
    3398:	93407c00 	sxtw	x0, w0
    339c:	d37ef400 	lsl	x0, x0, #2
    33a0:	910083a1 	add	x1, x29, #0x20
    33a4:	b8606820 	ldr	w0, [x1, x0]
    33a8:	b900bfa0 	str	w0, [x29, #188]
    33ac:	b980e3a0 	ldrsw	x0, [x29, #224]
    33b0:	d37ff800 	lsl	x0, x0, #1
    33b4:	910203a1 	add	x1, x29, #0x80
    33b8:	78e06820 	ldrsh	w0, [x1, x0]
    33bc:	b900bba0 	str	w0, [x29, #184]
    33c0:	b940e7a1 	ldr	w1, [x29, #228]
    33c4:	b940c3a0 	ldr	w0, [x29, #192]
    33c8:	0b000020 	add	w0, w1, w0
    33cc:	7100001f 	cmp	w0, #0x0
    33d0:	5400112b 	b.lt	35f4 <ImageProcess_Dither_T2001+0x664>  // b.tstop
    33d4:	b940e7a1 	ldr	w1, [x29, #228]
    33d8:	b940c3a0 	ldr	w0, [x29, #192]
    33dc:	0b000020 	add	w0, w1, w0
    33e0:	b94017a1 	ldr	w1, [x29, #20]
    33e4:	6b00003f 	cmp	w1, w0
    33e8:	5400106d 	b.le	35f4 <ImageProcess_Dither_T2001+0x664>
    33ec:	b940eba1 	ldr	w1, [x29, #232]
    33f0:	b940bfa0 	ldr	w0, [x29, #188]
    33f4:	0b000020 	add	w0, w1, w0
    33f8:	b94013a1 	ldr	w1, [x29, #16]
    33fc:	6b00003f 	cmp	w1, w0
    3400:	54000fed 	b.le	35fc <ImageProcess_Dither_T2001+0x66c>
    3404:	b940bfa0 	ldr	w0, [x29, #188]
    3408:	7100001f 	cmp	w0, #0x0
    340c:	54000100 	b.eq	342c <ImageProcess_Dither_T2001+0x49c>  // b.none
    3410:	b940bfa0 	ldr	w0, [x29, #188]
    3414:	7100041f 	cmp	w0, #0x1
    3418:	54000061 	b.ne	3424 <ImageProcess_Dither_T2001+0x494>  // b.any
    341c:	52800020 	mov	w0, #0x1                   	// #1
    3420:	14000004 	b	3430 <ImageProcess_Dither_T2001+0x4a0>
    3424:	52800040 	mov	w0, #0x2                   	// #2
    3428:	14000002 	b	3430 <ImageProcess_Dither_T2001+0x4a0>
    342c:	52800000 	mov	w0, #0x0                   	// #0
    3430:	b900b7a0 	str	w0, [x29, #180]
    3434:	b980b7a0 	ldrsw	x0, [x29, #180]
    3438:	d37df000 	lsl	x0, x0, #3
    343c:	910263a1 	add	x1, x29, #0x98
    3440:	f8606822 	ldr	x2, [x1, x0]
    3444:	b940e7a1 	ldr	w1, [x29, #228]
    3448:	b940c3a0 	ldr	w0, [x29, #192]
    344c:	0b000020 	add	w0, w1, w0
    3450:	93407c01 	sxtw	x1, w0
    3454:	aa0103e0 	mov	x0, x1
    3458:	d37ff800 	lsl	x0, x0, #1
    345c:	8b010000 	add	x0, x0, x1
    3460:	d37ff800 	lsl	x0, x0, #1
    3464:	8b000040 	add	x0, x2, x0
    3468:	79c00000 	ldrsh	w0, [x0]
    346c:	12003c01 	and	w1, w0, #0xffff
    3470:	b940cfa2 	ldr	w2, [x29, #204]
    3474:	b940bba0 	ldr	w0, [x29, #184]
    3478:	1b007c40 	mul	w0, w2, w0
    347c:	13067c00 	asr	w0, w0, #6
    3480:	12003c00 	and	w0, w0, #0xffff
    3484:	0b000020 	add	w0, w1, w0
    3488:	12003c03 	and	w3, w0, #0xffff
    348c:	b980b7a0 	ldrsw	x0, [x29, #180]
    3490:	d37df000 	lsl	x0, x0, #3
    3494:	910263a1 	add	x1, x29, #0x98
    3498:	f8606822 	ldr	x2, [x1, x0]
    349c:	b940e7a1 	ldr	w1, [x29, #228]
    34a0:	b940c3a0 	ldr	w0, [x29, #192]
    34a4:	0b000020 	add	w0, w1, w0
    34a8:	93407c01 	sxtw	x1, w0
    34ac:	aa0103e0 	mov	x0, x1
    34b0:	d37ff800 	lsl	x0, x0, #1
    34b4:	8b010000 	add	x0, x0, x1
    34b8:	d37ff800 	lsl	x0, x0, #1
    34bc:	8b000040 	add	x0, x2, x0
    34c0:	13003c61 	sxth	w1, w3
    34c4:	79000001 	strh	w1, [x0]
    34c8:	b980b7a0 	ldrsw	x0, [x29, #180]
    34cc:	d37df000 	lsl	x0, x0, #3
    34d0:	910263a1 	add	x1, x29, #0x98
    34d4:	f8606822 	ldr	x2, [x1, x0]
    34d8:	b940e7a1 	ldr	w1, [x29, #228]
    34dc:	b940c3a0 	ldr	w0, [x29, #192]
    34e0:	0b000020 	add	w0, w1, w0
    34e4:	93407c01 	sxtw	x1, w0
    34e8:	aa0103e0 	mov	x0, x1
    34ec:	d37ff800 	lsl	x0, x0, #1
    34f0:	8b010000 	add	x0, x0, x1
    34f4:	d37ff800 	lsl	x0, x0, #1
    34f8:	8b000040 	add	x0, x2, x0
    34fc:	79c00400 	ldrsh	w0, [x0, #2]
    3500:	12003c01 	and	w1, w0, #0xffff
    3504:	b940cba2 	ldr	w2, [x29, #200]
    3508:	b940bba0 	ldr	w0, [x29, #184]
    350c:	1b007c40 	mul	w0, w2, w0
    3510:	13067c00 	asr	w0, w0, #6
    3514:	12003c00 	and	w0, w0, #0xffff
    3518:	0b000020 	add	w0, w1, w0
    351c:	12003c03 	and	w3, w0, #0xffff
    3520:	b980b7a0 	ldrsw	x0, [x29, #180]
    3524:	d37df000 	lsl	x0, x0, #3
    3528:	910263a1 	add	x1, x29, #0x98
    352c:	f8606822 	ldr	x2, [x1, x0]
    3530:	b940e7a1 	ldr	w1, [x29, #228]
    3534:	b940c3a0 	ldr	w0, [x29, #192]
    3538:	0b000020 	add	w0, w1, w0
    353c:	93407c01 	sxtw	x1, w0
    3540:	aa0103e0 	mov	x0, x1
    3544:	d37ff800 	lsl	x0, x0, #1
    3548:	8b010000 	add	x0, x0, x1
    354c:	d37ff800 	lsl	x0, x0, #1
    3550:	8b000040 	add	x0, x2, x0
    3554:	13003c61 	sxth	w1, w3
    3558:	79000401 	strh	w1, [x0, #2]
    355c:	b980b7a0 	ldrsw	x0, [x29, #180]
    3560:	d37df000 	lsl	x0, x0, #3
    3564:	910263a1 	add	x1, x29, #0x98
    3568:	f8606822 	ldr	x2, [x1, x0]
    356c:	b940e7a1 	ldr	w1, [x29, #228]
    3570:	b940c3a0 	ldr	w0, [x29, #192]
    3574:	0b000020 	add	w0, w1, w0
    3578:	93407c01 	sxtw	x1, w0
    357c:	aa0103e0 	mov	x0, x1
    3580:	d37ff800 	lsl	x0, x0, #1
    3584:	8b010000 	add	x0, x0, x1
    3588:	d37ff800 	lsl	x0, x0, #1
    358c:	8b000040 	add	x0, x2, x0
    3590:	79c00800 	ldrsh	w0, [x0, #4]
    3594:	12003c01 	and	w1, w0, #0xffff
    3598:	b940c7a2 	ldr	w2, [x29, #196]
    359c:	b940bba0 	ldr	w0, [x29, #184]
    35a0:	1b007c40 	mul	w0, w2, w0
    35a4:	13067c00 	asr	w0, w0, #6
    35a8:	12003c00 	and	w0, w0, #0xffff
    35ac:	0b000020 	add	w0, w1, w0
    35b0:	12003c03 	and	w3, w0, #0xffff
    35b4:	b980b7a0 	ldrsw	x0, [x29, #180]
    35b8:	d37df000 	lsl	x0, x0, #3
    35bc:	910263a1 	add	x1, x29, #0x98
    35c0:	f8606822 	ldr	x2, [x1, x0]
    35c4:	b940e7a1 	ldr	w1, [x29, #228]
    35c8:	b940c3a0 	ldr	w0, [x29, #192]
    35cc:	0b000020 	add	w0, w1, w0
    35d0:	93407c01 	sxtw	x1, w0
    35d4:	aa0103e0 	mov	x0, x1
    35d8:	d37ff800 	lsl	x0, x0, #1
    35dc:	8b010000 	add	x0, x0, x1
    35e0:	d37ff800 	lsl	x0, x0, #1
    35e4:	8b000040 	add	x0, x2, x0
    35e8:	13003c61 	sxth	w1, w3
    35ec:	79000801 	strh	w1, [x0, #4]
    35f0:	14000004 	b	3600 <ImageProcess_Dither_T2001+0x670>
    35f4:	d503201f 	nop
    35f8:	14000002 	b	3600 <ImageProcess_Dither_T2001+0x670>
    35fc:	d503201f 	nop
    3600:	b940e3a0 	ldr	w0, [x29, #224]
    3604:	11000400 	add	w0, w0, #0x1
    3608:	b900e3a0 	str	w0, [x29, #224]
    360c:	b940e3a0 	ldr	w0, [x29, #224]
    3610:	71002c1f 	cmp	w0, #0xb
    3614:	54ffeaed 	b.le	3370 <ImageProcess_Dither_T2001+0x3e0>
    3618:	b940e7a0 	ldr	w0, [x29, #228]
    361c:	11000400 	add	w0, w0, #0x1
    3620:	b900e7a0 	str	w0, [x29, #228]
    3624:	b940e7a1 	ldr	w1, [x29, #228]
    3628:	b94017a0 	ldr	w0, [x29, #20]
    362c:	6b00003f 	cmp	w1, w0
    3630:	54ffd54b 	b.lt	30d8 <ImageProcess_Dither_T2001+0x148>  // b.tstop
    3634:	f9404fa3 	ldr	x3, [x29, #152]
    3638:	f94053a4 	ldr	x4, [x29, #160]
    363c:	b98017a1 	ldrsw	x1, [x29, #20]
    3640:	aa0103e0 	mov	x0, x1
    3644:	d37ff800 	lsl	x0, x0, #1
    3648:	8b010000 	add	x0, x0, x1
    364c:	d37ff800 	lsl	x0, x0, #1
    3650:	aa0003e2 	mov	x2, x0
    3654:	aa0403e1 	mov	x1, x4
    3658:	aa0303e0 	mov	x0, x3
    365c:	97fff58d 	bl	c90 <memcpy@plt>
    3660:	f94053a3 	ldr	x3, [x29, #160]
    3664:	f94057a4 	ldr	x4, [x29, #168]
    3668:	b98017a1 	ldrsw	x1, [x29, #20]
    366c:	aa0103e0 	mov	x0, x1
    3670:	d37ff800 	lsl	x0, x0, #1
    3674:	8b010000 	add	x0, x0, x1
    3678:	d37ff800 	lsl	x0, x0, #1
    367c:	aa0003e2 	mov	x2, x0
    3680:	aa0403e1 	mov	x1, x4
    3684:	aa0303e0 	mov	x0, x3
    3688:	97fff582 	bl	c90 <memcpy@plt>
    368c:	f94057a3 	ldr	x3, [x29, #168]
    3690:	b98017a1 	ldrsw	x1, [x29, #20]
    3694:	aa0103e0 	mov	x0, x1
    3698:	d37ff800 	lsl	x0, x0, #1
    369c:	8b010000 	add	x0, x0, x1
    36a0:	d37ff800 	lsl	x0, x0, #1
    36a4:	aa0003e2 	mov	x2, x0
    36a8:	52800001 	mov	w1, #0x0                   	// #0
    36ac:	aa0303e0 	mov	x0, x3
    36b0:	97fff58c 	bl	ce0 <memset@plt>
    36b4:	b940eba0 	ldr	w0, [x29, #232]
    36b8:	11000400 	add	w0, w0, #0x1
    36bc:	b900eba0 	str	w0, [x29, #232]
    36c0:	b940eba1 	ldr	w1, [x29, #232]
    36c4:	b94013a0 	ldr	w0, [x29, #16]
    36c8:	6b00003f 	cmp	w1, w0
    36cc:	54ffd02b 	b.lt	30d0 <ImageProcess_Dither_T2001+0x140>  // b.tstop
    36d0:	f9404fa0 	ldr	x0, [x29, #152]
    36d4:	97fff597 	bl	d30 <free@plt>
    36d8:	f94053a0 	ldr	x0, [x29, #160]
    36dc:	97fff595 	bl	d30 <free@plt>
    36e0:	f94057a0 	ldr	x0, [x29, #168]
    36e4:	97fff593 	bl	d30 <free@plt>
    36e8:	a8cf7bfd 	ldp	x29, x30, [sp], #240
    36ec:	d65f03c0 	ret

00000000000036f0 <ImageProcess_Spectra6_T2001>:
    36f0:	a9bd7bfd 	stp	x29, x30, [sp, #-48]!
    36f4:	910003fd 	mov	x29, sp
    36f8:	bd002fa0 	str	s0, [x29, #44]
    36fc:	bd002ba1 	str	s1, [x29, #40]
    3700:	bd0027a2 	str	s2, [x29, #36]
    3704:	bd0023a3 	str	s3, [x29, #32]
    3708:	f9000fa0 	str	x0, [x29, #24]
    370c:	b90017a1 	str	w1, [x29, #20]
    3710:	b90013a2 	str	w2, [x29, #16]
    3714:	b94013a2 	ldr	w2, [x29, #16]
    3718:	b94017a1 	ldr	w1, [x29, #20]
    371c:	f9400fa0 	ldr	x0, [x29, #24]
    3720:	bd4023a3 	ldr	s3, [x29, #32]
    3724:	bd4027a2 	ldr	s2, [x29, #36]
    3728:	bd402ba1 	ldr	s1, [x29, #40]
    372c:	bd402fa0 	ldr	s0, [x29, #44]
    3730:	97fff55c 	bl	ca0 <ImageProcess_ColorEnhace@plt>
    3734:	b94013a2 	ldr	w2, [x29, #16]
    3738:	b94017a1 	ldr	w1, [x29, #20]
    373c:	f9400fa0 	ldr	x0, [x29, #24]
    3740:	97fffe14 	bl	2f90 <ImageProcess_Dither_T2001>
    3744:	d503201f 	nop
    3748:	a8c37bfd 	ldp	x29, x30, [sp], #48
    374c:	d65f03c0 	ret

0000000000003750 <IndexMapping_Spectra6_T2001_Y8_4bit>:
    3750:	a9bc7bfd 	stp	x29, x30, [sp, #-64]!
    3754:	910003fd 	mov	x29, sp
    3758:	f90017a0 	str	x0, [x29, #40]
    375c:	f90013a1 	str	x1, [x29, #32]
    3760:	b9001fa2 	str	w2, [x29, #28]
    3764:	b9001ba3 	str	w3, [x29, #24]
    3768:	b9401ba2 	ldr	w2, [x29, #24]
    376c:	b9401fa1 	ldr	w1, [x29, #28]
    3770:	f94017a0 	ldr	x0, [x29, #40]
    3774:	0f000403 	movi	v3.2s, #0x0
    3778:	52a84183 	mov	w3, #0x420c0000            	// #1108082688
    377c:	1e270062 	fmov	s2, w3
    3780:	529999a3 	mov	w3, #0xcccd                	// #52429
    3784:	72a7b983 	movk	w3, #0x3dcc, lsl #16
    3788:	1e270061 	fmov	s1, w3
    378c:	1e2f1000 	fmov	s0, #1.500000000000000000e+00
    3790:	97ffffd8 	bl	36f0 <ImageProcess_Spectra6_T2001>
    3794:	b9003fbf 	str	wzr, [x29, #60]
    3798:	b9003bbf 	str	wzr, [x29, #56]
    379c:	1400004a 	b	38c4 <IndexMapping_Spectra6_T2001_Y8_4bit+0x174>
    37a0:	b9803fa0 	ldrsw	x0, [x29, #60]
    37a4:	f94017a1 	ldr	x1, [x29, #40]
    37a8:	8b000020 	add	x0, x1, x0
    37ac:	39400000 	ldrb	w0, [x0]
    37b0:	3900cfa0 	strb	w0, [x29, #51]
    37b4:	b9803fa0 	ldrsw	x0, [x29, #60]
    37b8:	91000400 	add	x0, x0, #0x1
    37bc:	f94017a1 	ldr	x1, [x29, #40]
    37c0:	8b000020 	add	x0, x1, x0
    37c4:	39400000 	ldrb	w0, [x0]
    37c8:	3900cba0 	strb	w0, [x29, #50]
    37cc:	b9803fa0 	ldrsw	x0, [x29, #60]
    37d0:	91000800 	add	x0, x0, #0x2
    37d4:	f94017a1 	ldr	x1, [x29, #40]
    37d8:	8b000020 	add	x0, x1, x0
    37dc:	39400000 	ldrb	w0, [x0]
    37e0:	3900c7a0 	strb	w0, [x29, #49]
    37e4:	b90037bf 	str	wzr, [x29, #52]
    37e8:	1400002e 	b	38a0 <IndexMapping_Spectra6_T2001_Y8_4bit+0x150>
    37ec:	90000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    37f0:	f947e002 	ldr	x2, [x0, #4032]
    37f4:	b98037a1 	ldrsw	x1, [x29, #52]
    37f8:	aa0103e0 	mov	x0, x1
    37fc:	d37ff800 	lsl	x0, x0, #1
    3800:	8b010000 	add	x0, x0, x1
    3804:	8b000040 	add	x0, x2, x0
    3808:	39400000 	ldrb	w0, [x0]
    380c:	3940cfa1 	ldrb	w1, [x29, #51]
    3810:	6b00003f 	cmp	w1, w0
    3814:	54000401 	b.ne	3894 <IndexMapping_Spectra6_T2001_Y8_4bit+0x144>  // b.any
    3818:	90000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    381c:	f947e002 	ldr	x2, [x0, #4032]
    3820:	b98037a1 	ldrsw	x1, [x29, #52]
    3824:	aa0103e0 	mov	x0, x1
    3828:	d37ff800 	lsl	x0, x0, #1
    382c:	8b010000 	add	x0, x0, x1
    3830:	8b000040 	add	x0, x2, x0
    3834:	39400400 	ldrb	w0, [x0, #1]
    3838:	3940cba1 	ldrb	w1, [x29, #50]
    383c:	6b00003f 	cmp	w1, w0
    3840:	540002a1 	b.ne	3894 <IndexMapping_Spectra6_T2001_Y8_4bit+0x144>  // b.any
    3844:	90000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    3848:	f947e002 	ldr	x2, [x0, #4032]
    384c:	b98037a1 	ldrsw	x1, [x29, #52]
    3850:	aa0103e0 	mov	x0, x1
    3854:	d37ff800 	lsl	x0, x0, #1
    3858:	8b010000 	add	x0, x0, x1
    385c:	8b000040 	add	x0, x2, x0
    3860:	39400800 	ldrb	w0, [x0, #2]
    3864:	3940c7a1 	ldrb	w1, [x29, #49]
    3868:	6b00003f 	cmp	w1, w0
    386c:	54000141 	b.ne	3894 <IndexMapping_Spectra6_T2001_Y8_4bit+0x144>  // b.any
    3870:	b9803ba0 	ldrsw	x0, [x29, #56]
    3874:	f94013a1 	ldr	x1, [x29, #32]
    3878:	8b000020 	add	x0, x1, x0
    387c:	90000081 	adrp	x1, 13000 <__FRAME_END__+0xf4b0>
    3880:	f947d822 	ldr	x2, [x1, #4016]
    3884:	b98037a1 	ldrsw	x1, [x29, #52]
    3888:	38616841 	ldrb	w1, [x2, x1]
    388c:	39000001 	strb	w1, [x0]
    3890:	14000007 	b	38ac <IndexMapping_Spectra6_T2001_Y8_4bit+0x15c>
    3894:	b94037a0 	ldr	w0, [x29, #52]
    3898:	11000400 	add	w0, w0, #0x1
    389c:	b90037a0 	str	w0, [x29, #52]
    38a0:	b94037a0 	ldr	w0, [x29, #52]
    38a4:	7100241f 	cmp	w0, #0x9
    38a8:	54fffa2d 	b.le	37ec <IndexMapping_Spectra6_T2001_Y8_4bit+0x9c>
    38ac:	b9403fa0 	ldr	w0, [x29, #60]
    38b0:	11000c00 	add	w0, w0, #0x3
    38b4:	b9003fa0 	str	w0, [x29, #60]
    38b8:	b9403ba0 	ldr	w0, [x29, #56]
    38bc:	11000400 	add	w0, w0, #0x1
    38c0:	b9003ba0 	str	w0, [x29, #56]
    38c4:	b9401fa1 	ldr	w1, [x29, #28]
    38c8:	b9401ba0 	ldr	w0, [x29, #24]
    38cc:	1b007c20 	mul	w0, w1, w0
    38d0:	b9403ba1 	ldr	w1, [x29, #56]
    38d4:	6b00003f 	cmp	w1, w0
    38d8:	54fff64b 	b.lt	37a0 <IndexMapping_Spectra6_T2001_Y8_4bit+0x50>  // b.tstop
    38dc:	d503201f 	nop
    38e0:	a8c47bfd 	ldp	x29, x30, [sp], #64
    38e4:	d65f03c0 	ret

00000000000038e8 <IndexMapping_Spectra6_T2001_Y8_5bit>:
    38e8:	a9bc7bfd 	stp	x29, x30, [sp, #-64]!
    38ec:	910003fd 	mov	x29, sp
    38f0:	f90017a0 	str	x0, [x29, #40]
    38f4:	f90013a1 	str	x1, [x29, #32]
    38f8:	b9001fa2 	str	w2, [x29, #28]
    38fc:	b9001ba3 	str	w3, [x29, #24]
    3900:	b9401ba2 	ldr	w2, [x29, #24]
    3904:	b9401fa1 	ldr	w1, [x29, #28]
    3908:	f94017a0 	ldr	x0, [x29, #40]
    390c:	0f000403 	movi	v3.2s, #0x0
    3910:	52a84183 	mov	w3, #0x420c0000            	// #1108082688
    3914:	1e270062 	fmov	s2, w3
    3918:	529999a3 	mov	w3, #0xcccd                	// #52429
    391c:	72a7b983 	movk	w3, #0x3dcc, lsl #16
    3920:	1e270061 	fmov	s1, w3
    3924:	1e2f1000 	fmov	s0, #1.500000000000000000e+00
    3928:	97ffff72 	bl	36f0 <ImageProcess_Spectra6_T2001>
    392c:	b9003fbf 	str	wzr, [x29, #60]
    3930:	b9003bbf 	str	wzr, [x29, #56]
    3934:	1400004a 	b	3a5c <IndexMapping_Spectra6_T2001_Y8_5bit+0x174>
    3938:	b9803fa0 	ldrsw	x0, [x29, #60]
    393c:	f94017a1 	ldr	x1, [x29, #40]
    3940:	8b000020 	add	x0, x1, x0
    3944:	39400000 	ldrb	w0, [x0]
    3948:	3900cfa0 	strb	w0, [x29, #51]
    394c:	b9803fa0 	ldrsw	x0, [x29, #60]
    3950:	91000400 	add	x0, x0, #0x1
    3954:	f94017a1 	ldr	x1, [x29, #40]
    3958:	8b000020 	add	x0, x1, x0
    395c:	39400000 	ldrb	w0, [x0]
    3960:	3900cba0 	strb	w0, [x29, #50]
    3964:	b9803fa0 	ldrsw	x0, [x29, #60]
    3968:	91000800 	add	x0, x0, #0x2
    396c:	f94017a1 	ldr	x1, [x29, #40]
    3970:	8b000020 	add	x0, x1, x0
    3974:	39400000 	ldrb	w0, [x0]
    3978:	3900c7a0 	strb	w0, [x29, #49]
    397c:	b90037bf 	str	wzr, [x29, #52]
    3980:	1400002e 	b	3a38 <IndexMapping_Spectra6_T2001_Y8_5bit+0x150>
    3984:	90000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    3988:	f947e002 	ldr	x2, [x0, #4032]
    398c:	b98037a1 	ldrsw	x1, [x29, #52]
    3990:	aa0103e0 	mov	x0, x1
    3994:	d37ff800 	lsl	x0, x0, #1
    3998:	8b010000 	add	x0, x0, x1
    399c:	8b000040 	add	x0, x2, x0
    39a0:	39400000 	ldrb	w0, [x0]
    39a4:	3940cfa1 	ldrb	w1, [x29, #51]
    39a8:	6b00003f 	cmp	w1, w0
    39ac:	54000401 	b.ne	3a2c <IndexMapping_Spectra6_T2001_Y8_5bit+0x144>  // b.any
    39b0:	90000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    39b4:	f947e002 	ldr	x2, [x0, #4032]
    39b8:	b98037a1 	ldrsw	x1, [x29, #52]
    39bc:	aa0103e0 	mov	x0, x1
    39c0:	d37ff800 	lsl	x0, x0, #1
    39c4:	8b010000 	add	x0, x0, x1
    39c8:	8b000040 	add	x0, x2, x0
    39cc:	39400400 	ldrb	w0, [x0, #1]
    39d0:	3940cba1 	ldrb	w1, [x29, #50]
    39d4:	6b00003f 	cmp	w1, w0
    39d8:	540002a1 	b.ne	3a2c <IndexMapping_Spectra6_T2001_Y8_5bit+0x144>  // b.any
    39dc:	90000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
    39e0:	f947e002 	ldr	x2, [x0, #4032]
    39e4:	b98037a1 	ldrsw	x1, [x29, #52]
    39e8:	aa0103e0 	mov	x0, x1
    39ec:	d37ff800 	lsl	x0, x0, #1
    39f0:	8b010000 	add	x0, x0, x1
    39f4:	8b000040 	add	x0, x2, x0
    39f8:	39400800 	ldrb	w0, [x0, #2]
    39fc:	3940c7a1 	ldrb	w1, [x29, #49]
    3a00:	6b00003f 	cmp	w1, w0
    3a04:	54000141 	b.ne	3a2c <IndexMapping_Spectra6_T2001_Y8_5bit+0x144>  // b.any
    3a08:	b9803ba0 	ldrsw	x0, [x29, #56]
    3a0c:	f94013a1 	ldr	x1, [x29, #32]
    3a10:	8b000020 	add	x0, x1, x0
    3a14:	90000081 	adrp	x1, 13000 <__FRAME_END__+0xf4b0>
    3a18:	f947ec22 	ldr	x2, [x1, #4056]
    3a1c:	b98037a1 	ldrsw	x1, [x29, #52]
    3a20:	38616841 	ldrb	w1, [x2, x1]
    3a24:	39000001 	strb	w1, [x0]
    3a28:	14000007 	b	3a44 <IndexMapping_Spectra6_T2001_Y8_5bit+0x15c>
    3a2c:	b94037a0 	ldr	w0, [x29, #52]
    3a30:	11000400 	add	w0, w0, #0x1
    3a34:	b90037a0 	str	w0, [x29, #52]
    3a38:	b94037a0 	ldr	w0, [x29, #52]
    3a3c:	7100241f 	cmp	w0, #0x9
    3a40:	54fffa2d 	b.le	3984 <IndexMapping_Spectra6_T2001_Y8_5bit+0x9c>
    3a44:	b9403fa0 	ldr	w0, [x29, #60]
    3a48:	11000c00 	add	w0, w0, #0x3
    3a4c:	b9003fa0 	str	w0, [x29, #60]
    3a50:	b9403ba0 	ldr	w0, [x29, #56]
    3a54:	11000400 	add	w0, w0, #0x1
    3a58:	b9003ba0 	str	w0, [x29, #56]
    3a5c:	b9401fa1 	ldr	w1, [x29, #28]
    3a60:	b9401ba0 	ldr	w0, [x29, #24]
    3a64:	1b007c20 	mul	w0, w1, w0
    3a68:	b9403ba1 	ldr	w1, [x29, #56]
    3a6c:	6b00003f 	cmp	w1, w0
    3a70:	54fff64b 	b.lt	3938 <IndexMapping_Spectra6_T2001_Y8_5bit+0x50>  // b.tstop
    3a74:	d503201f 	nop
    3a78:	a8c47bfd 	ldp	x29, x30, [sp], #64
    3a7c:	d65f03c0 	ret

Disassembly of section .fini:

0000000000003a80 <_fini>:
    3a80:	a9bf7bfd 	stp	x29, x30, [sp, #-16]!
    3a84:	910003fd 	mov	x29, sp
    3a88:	a8c17bfd 	ldp	x29, x30, [sp], #16
    3a8c:	d65f03c0 	ret
