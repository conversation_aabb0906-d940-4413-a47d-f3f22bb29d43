# Image Tools - ARM64 Assembly to C Conversion

This project contains image processing functions that were originally implemented in ARM64 assembly language and have been converted to portable C code.

## Overview

The original `image_tools.o` file contained ARM64 assembly code for various image processing operations. This code has been analyzed and converted to equivalent C functions for better portability, maintainability, and readability.

## Converted Functions

### Color Format Conversion Functions

1. **`SWAP_RGB_BGR`** - Swaps RGB and BGR color channels
   - Converts RGB format to BGR format or vice versa
   - Useful for different image format requirements

2. **`bgr888_2_Y8`** - Converts BGR888 to 8-bit grayscale
   - Uses ITU-R BT.601 luma coefficients: Y = 0.114*B + 0.587*G + 0.299*R
   - Optimized integer arithmetic for performance

3. **`rgb888_2_Y8`** - Converts RGB888 to 8-bit grayscale
   - Uses ITU-R BT.601 luma coefficients: Y = 0.299*R + 0.587*G + 0.114*B
   - Standard RGB to grayscale conversion

4. **`Y8ToY4`** - Converts 8-bit grayscale to 4-bit grayscale
   - Packs two 4-bit pixels into one byte for memory efficiency
   - Useful for low-memory applications

5. **`bgr888_2_Y1`** - Converts BGR888 to 1-bit monochrome
   - Applies threshold-based binarization
   - Packs 8 pixels into one byte

6. **`rgb888_2_Y1`** - Converts RGB888 to 1-bit monochrome
   - Similar to BGR version but for RGB input
   - Threshold value: 128

### Advanced Processing Functions

7. **`kaleido_s1_transfer_ec253tt1`** - Complex image transformation
   - Performs HSV-based color enhancement
   - Includes saturation boost and image scaling
   - Simplified from the original complex assembly implementation

8. **`adjustColor`** - Single pixel color adjustment
   - Adjusts saturation, brightness, contrast, and hue
   - Uses HSV color space for natural color manipulation

9. **`ImageProcess_ColorEnhace`** - Batch color enhancement
   - Applies color adjustments to entire images
   - Calls `adjustColor` for each pixel

10. **`find_nearest_color_AIO`** - Nearest color matching
    - Finds closest color in a predefined palette
    - Uses Euclidean distance in RGB space

## Files

- `image_tools.h` - Header file with function declarations and constants
- `image_tools.c` - Implementation of all converted functions
- `test_image_tools.c` - Test program demonstrating function usage
- `Makefile` - Build configuration
- `README.md` - This documentation

## Building and Testing

### Prerequisites
- GCC compiler with C99 support
- Make utility
- Math library (libm)

### Build Commands

```bash
# Build the test program
make

# Build and run tests
make test

# Clean build artifacts
make clean

# Show help
make help
```

### Running Tests

```bash
./test_image_tools
```

The test program demonstrates each function with sample data and shows the results.

## Key Differences from Assembly

1. **Portability**: C code runs on any platform with a C compiler
2. **Readability**: Much easier to understand and maintain
3. **Debugging**: Standard debugging tools can be used
4. **Optimization**: Modern compilers can optimize effectively
5. **Safety**: Better bounds checking and type safety

## Performance Considerations

- The C versions maintain the algorithmic efficiency of the original assembly
- Some assembly-specific optimizations (like magic number division) are preserved
- Modern compilers can often produce assembly code comparable to hand-written assembly
- For critical performance paths, profile-guided optimization is recommended

## Usage Example

```c
#include "image_tools.h"

// Convert RGB image to grayscale
uint8_t rgb_data[width * height * 3];
uint8_t gray_data[width * height];

rgb888_2_Y8(rgb_data, gray_data, width, height);

// Enhance colors
float saturation = 1.5f;
float brightness = 1.1f; 
float contrast = 1.2f;
float hue = 0.0f;

ImageProcess_ColorEnhace(saturation, brightness, contrast, hue, 
                        rgb_data, width, height);
```

## Notes

- Some functions use simplified implementations compared to the original assembly
- The complex `kaleido_s1_transfer_ec253tt1` function is a simplified version
- Color palette in `find_nearest_color_AIO` is a sample - replace with actual palette
- All functions assume valid input parameters - add bounds checking as needed

## License

This code is provided as-is for educational and development purposes.
