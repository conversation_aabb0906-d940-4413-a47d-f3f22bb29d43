# Makefile for image_tools project

CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -O2 -g
LDFLAGS = -lm

# Source files
SOURCES = image_tools.c test_image_tools.c spectra6_demo.c
OBJECTS = $(SOURCES:.c=.o)
TARGET = test_image_tools
DEMO_TARGET = spectra6_demo

# Header files
HEADERS = image_tools.h

.PHONY: all clean test demo

all: $(TARGET) $(DEMO_TARGET)

$(TARGET): image_tools.o test_image_tools.o
	$(CC) image_tools.o test_image_tools.o -o $(TARGET) $(LDFLAGS)

$(DEMO_TARGET): image_tools.o spectra6_demo.o
	$(CC) image_tools.o spectra6_demo.o -o $(DEMO_TARGET) $(LDFLAGS)

%.o: %.c $(HEADERS)
	$(CC) $(CFLAGS) -c $< -o $@

test: $(TARGET)
	./$(TARGET)

demo: $(DEMO_TARGET)
	./$(DEMO_TARGET)

clean:
	rm -f $(OBJECTS) $(TARGET) $(DEMO_TARGET)

# Individual object file rules
image_tools.o: image_tools.c image_tools.h
	$(CC) $(CFLAGS) -c image_tools.c -o image_tools.o

test_image_tools.o: test_image_tools.c image_tools.h
	$(CC) $(CFLAGS) -c test_image_tools.c -o test_image_tools.o

spectra6_demo.o: spectra6_demo.c image_tools.h
	$(CC) $(CFLAGS) -c spectra6_demo.c -o spectra6_demo.o

# Help target
help:
	@echo "Available targets:"
	@echo "  all     - Build both test program and demo"
	@echo "  test    - Build and run the test program"
	@echo "  demo    - Build and run the Spectra6 demo"
	@echo "  clean   - Remove object files and executables"
	@echo "  help    - Show this help message"
