# Makefile for image_tools project

CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -O2 -g
LDFLAGS = -lm

# Source files
SOURCES = image_tools.c test_image_tools.c
OBJECTS = $(SOURCES:.c=.o)
TARGET = test_image_tools

# Header files
HEADERS = image_tools.h

.PHONY: all clean test

all: $(TARGET)

$(TARGET): $(OBJECTS)
	$(CC) $(OBJECTS) -o $(TARGET) $(LDFLAGS)

%.o: %.c $(HEADERS)
	$(CC) $(CFLAGS) -c $< -o $@

test: $(TARGET)
	./$(TARGET)

clean:
	rm -f $(OBJECTS) $(TARGET)

# Individual object file rules
image_tools.o: image_tools.c image_tools.h
	$(CC) $(CFLAGS) -c image_tools.c -o image_tools.o

test_image_tools.o: test_image_tools.c image_tools.h
	$(CC) $(CFLAGS) -c test_image_tools.c -o test_image_tools.o

# Help target
help:
	@echo "Available targets:"
	@echo "  all     - Build the test program"
	@echo "  test    - Build and run the test program"
	@echo "  clean   - Remove object files and executable"
	@echo "  help    - Show this help message"
