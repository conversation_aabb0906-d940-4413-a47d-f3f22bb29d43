# Spectra6 索引数组汇编分析

## 问题回答

您问的 `static const uint8_t spectra6_indices[6] = {0, 15, 8, 4, 2, 12};` 在汇编中对应以下位置：

## 汇编代码中的索引数组引用

### 1. IndexMapping_Spectra6_AIO_Y4 函数中的引用

**汇编位置：第1984-1987行**
```assembly
2a3c:	b0000080 	adrp	x0, 13000 <__FRAME_END__+0xf4b0>
2a40:	f947e801 	ldr	x1, [x0, #4048]           ; 加载索引数组地址
2a44:	b98057a0 	ldrsw	x0, [x29, #84]           ; 加载调色板索引
2a48:	38606820 	ldrb	w0, [x1, x0]             ; 从索引数组中读取映射值
```

**分析：**
- `adrp x0, 13000` - 加载数据段基地址
- `ldr x1, [x0, #4048]` - 从偏移4048处加载索引数组的地址
- `ldrb w0, [x1, x0]` - 使用调色板索引作为偏移，从索引数组中读取对应的映射值

### 2. IndexMapping_Spectra6_T2000_Y8 函数中的引用

**汇编位置：第2227-2230行**
```assembly
2df8:	b0000081 	adrp	x1, 13000 <__FRAME_END__+0xf4b0>
2dfc:	f947d422 	ldr	x2, [x1, #4008]           ; 加载T2000索引数组地址
2e00:	b98037a1 	ldrsw	x1, [x29, #52]           ; 加载调色板索引
2e04:	38616841 	ldrb	w1, [x2, x1]             ; 从T2000索引数组中读取映射值
```

**分析：**
- `adrp x1, 13000` - 加载数据段基地址
- `ldr x2, [x1, #4008]` - 从偏移4008处加载T2000索引数组的地址
- `ldrb w1, [x2, x1]` - 使用调色板索引读取T2000映射值

## 数据段地址映射

根据汇编代码分析，有两个不同的索引数组：

### 1. Spectra6 AIO 索引数组
- **内存地址**: `13000 + 4048 = 0x13FD0`
- **用途**: `IndexMapping_Spectra6_AIO_Y4` 函数
- **对应C代码**: `spectra6_indices[6] = {0, 15, 8, 4, 2, 12}`

### 2. Spectra6 T2000 索引数组  
- **内存地址**: `13000 + 4008 = 0x13FA8`
- **用途**: `IndexMapping_Spectra6_T2000_Y8` 函数
- **可能的值**: 不同于AIO版本的索引映射

## 索引值含义分析

基于汇编代码和测试结果，`{0, 15, 8, 4, 2, 12}` 这6个值对应：

| 调色板索引 | 颜色 | 映射值 | 二进制 | 含义 |
|------------|------|--------|--------|------|
| 0 | 黑色 (0,0,0) | 0 | 0000 | 最暗 |
| 1 | 白色 (255,255,255) | 15 | 1111 | 最亮 |
| 2 | 红色 (255,0,0) | 8 | 1000 | 中等亮度 |
| 3 | 绿色 (0,255,0) | 4 | 0100 | 中等亮度 |
| 4 | 蓝色 (0,0,255) | 2 | 0010 | 中等亮度 |
| 5 | 黄色 (255,255,0) | 12 | 1100 | 较亮 |

## 汇编指令详解

### 数据加载指令序列
```assembly
; 1. 加载数据段基地址
adrp    x0, 13000                    ; x0 = 数据段基地址

; 2. 加载索引数组地址
ldr     x1, [x0, #4048]             ; x1 = *(数据段基地址 + 4048)

; 3. 加载调色板索引
ldrsw   x0, [x29, #84]              ; x0 = 调色板索引 (0-5)

; 4. 读取映射值
ldrb    w0, [x1, x0]                ; w0 = 索引数组[调色板索引]
```

### 地址计算
- **数据段基地址**: `0x13000`
- **索引数组偏移**: `4048` (十进制) = `0xFD0` (十六进制)
- **最终地址**: `0x13000 + 0xFD0 = 0x13FD0`

## 在C代码中的实现

汇编中的这个索引数组在我们的C代码中对应：

```c
// 对应汇编中 13000+4048 处的数据
static const uint8_t spectra6_indices[6] = {0, 15, 8, 4, 2, 12};

// 使用方式
uint8_t mapped_value = spectra6_indices[palette_index];
```

## 验证

通过我们的测试程序验证：
- 红色 (索引2) → 映射值8 → Y4输出高4位为8
- 绿色 (索引3) → 映射值4 → Y4输出低4位为4
- 蓝色 (索引4) → 映射值2 → Y4输出高4位为2
- 黄色 (索引5) → 映射值12 → Y4输出低4位为12

测试输出 `0x84 0x2C` 完全符合这个映射关系。

## 总结

`{0, 15, 8, 4, 2, 12}` 这个数组在汇编中存储在数据段的 `0x13FD0` 地址处，通过 `adrp` 和 `ldr` 指令加载，然后使用 `ldrb` 指令根据调色板索引读取对应的4位映射值，用于Y4格式的像素打包。
