#ifndef IMAGE_TOOLS_H
#define IMAGE_TOOLS_H

#include <stdint.h>
#include <limits.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @file image_tools.h
 * @brief Image processing functions converted from ARM64 assembly
 * 
 * This header file contains declarations for various image processing functions
 * that were originally implemented in ARM64 assembly language and have been
 * converted to C for better portability and maintainability.
 */

// Function prototypes

/**
 * @brief Swap RGB and BGR color channels
 * @param src Source image data
 * @param dst Destination image data  
 * @param pixel_count Number of pixels to process
 */
void SWAP_RGB_BGR(uint8_t* src, uint8_t* dst, int pixel_count);

/**
 * @brief Convert BGR888 to Y8 (grayscale)
 * Uses ITU-R BT.601 luma coefficients: Y = 0.114*B + 0.587*G + 0.299*R
 * @param src Source BGR888 image data
 * @param dst Destination Y8 grayscale data
 * @param width Image width
 * @param height Image height
 */
void bgr888_2_Y8(uint8_t* src, uint8_t* dst, int width, int height);

/**
 * @brief Convert RGB888 to Y8 (grayscale)
 * Uses ITU-R BT.601 luma coefficients: Y = 0.299*R + 0.587*G + 0.114*B
 * @param src Source RGB888 image data
 * @param dst Destination Y8 grayscale data
 * @param width Image width
 * @param height Image height
 */
void rgb888_2_Y8(uint8_t* src, uint8_t* dst, int width, int height);

/**
 * @brief Convert Y8 (8-bit grayscale) to Y4 (4-bit grayscale)
 * Packs two 4-bit pixels into one byte
 * @param src Source Y8 grayscale data
 * @param dst Destination Y4 packed data
 * @param width Image width
 * @param height Image height
 */
void Y8ToY4(uint8_t* src, uint8_t* dst, int width, int height);

/**
 * @brief Convert BGR888 to Y1 (1-bit monochrome)
 * Converts to grayscale first, then applies threshold and packs 8 pixels per byte
 * @param src Source BGR888 image data
 * @param dst Destination Y1 packed data
 * @param width Image width
 * @param height Image height
 */
void bgr888_2_Y1(uint8_t* src, uint8_t* dst, int width, int height);

/**
 * @brief Convert RGB888 to Y1 (1-bit monochrome)
 * Converts to grayscale first, then applies threshold and packs 8 pixels per byte
 * @param src Source RGB888 image data
 * @param dst Destination Y1 packed data
 * @param width Image width
 * @param height Image height
 */
void rgb888_2_Y1(uint8_t* src, uint8_t* dst, int width, int height);

/**
 * @brief Complex image transformation function
 * Performs HSV-based color transformation and scaling
 * @param src Source image data
 * @param dst Destination image data
 * @param src_width Source image width
 * @param src_height Source image height
 * @param dst_width Destination image width
 * @param dst_height Destination image height
 */
void kaleido_s1_transfer_ec253tt1(uint8_t* src, uint8_t* dst, int src_width, int src_height, int dst_width, int dst_height);

/**
 * @brief Adjust color properties of a single pixel
 * @param pixel Pointer to RGB pixel data (3 bytes)
 * @param saturation Saturation adjustment factor
 * @param brightness Brightness adjustment factor  
 * @param contrast Contrast adjustment factor
 * @param hue Hue adjustment factor
 */
void adjustColor(uint8_t* pixel, float saturation, float brightness, float contrast, float hue);

/**
 * @brief Apply color enhancement to entire image
 * @param saturation Saturation adjustment factor
 * @param brightness Brightness adjustment factor
 * @param contrast Contrast adjustment factor
 * @param hue Hue adjustment factor
 * @param data Image data (RGB format)
 * @param width Image width
 * @param height Image height
 */
void ImageProcess_ColorEnhace(float saturation, float brightness, float contrast, float hue, uint8_t* data, int width, int height);

/**
 * @brief Find nearest color in palette
 * @param r Red component
 * @param g Green component  
 * @param b Blue component
 * @return Index of nearest color in palette
 */
int find_nearest_color_AIO(int r, int g, int b);

// Constants
#define MONOCHROME_THRESHOLD 128
#define MAX_PALETTE_COLORS 256

// Color palette structure
typedef struct {
    uint8_t r, g, b;
} ColorRGB;

#ifdef __cplusplus
}
#endif

#endif // IMAGE_TOOLS_H
