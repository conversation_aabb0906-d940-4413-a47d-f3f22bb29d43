# IndexMapping_Spectra6_AIO_Y4 函数分析与实现

## 概述

`IndexMapping_Spectra6_AIO_Y4` 是一个复杂的图像处理函数，从ARM64汇编代码转换而来。该函数的主要功能是将RGB888格式的图像数据映射到Spectra6颜色索引，并将结果打包为Y4格式（4位灰度）。

## 汇编代码分析

### 函数签名
```assembly
IndexMapping_Spectra6_AIO_Y4(uint8_t* src, uint8_t* dst, int width, int height)
```

### 关键参数识别
从汇编代码分析得出的颜色处理参数：
- **饱和度 (saturation)**: 1.5 (0x3f800000 + 0x3f000000)
- **亮度 (brightness)**: 0.05 (0x3d4ccccd)  
- **对比度 (contrast)**: 10.0 (0x41200000)
- **色调 (hue)**: 0.0 (0x00000000)

### 算法流程

1. **颜色预处理**
   ```assembly
   bl ImageProcess_Spectra6_AIO  ; 调用Spectra6颜色处理
   ```

2. **像素遍历循环**
   ```assembly
   ; 外层循环：遍历所有像素
   ; src_idx += 3 (RGB字节索引)
   ; dst_idx += 1 (像素索引)
   ```

3. **RGB提取**
   ```assembly
   ldrb w0, [x0]      ; R分量
   ldrb w0, [x0, #1]  ; G分量  
   ldrb w0, [x0, #2]  ; B分量
   ```

4. **颜色匹配**
   ```assembly
   ; 内层循环：在调色板中查找匹配颜色
   ; 比较RGB三个分量
   ; 循环最多6次 (Spectra6有6种颜色)
   ```

5. **Y4打包**
   ```assembly
   ; 检查像素索引奇偶性
   and w0, w0, #0x1
   ; 偶数像素：存储在高4位
   lsl w2, w0, #4
   ; 奇数像素：存储在低4位
   and w0, w0, #0xf
   ```

## C语言实现

### 主函数实现
```c
void IndexMapping_Spectra6_AIO_Y4(uint8_t* src, uint8_t* dst, int width, int height) {
    // 1. 颜色预处理
    float saturation = 1.5f;
    float brightness = 0.05f; 
    float contrast = 10.0f;
    float hue = 0.0f;
    
    ImageProcess_Spectra6_AIO(src, width, height, saturation, brightness, contrast, hue);
    
    // 2. 像素处理循环
    int src_idx = 0;
    int dst_idx = 0;
    int total_pixels = width * height;
    
    for (dst_idx = 0; dst_idx < total_pixels; dst_idx++) {
        // 3. RGB提取
        uint8_t r = src[src_idx];
        uint8_t g = src[src_idx + 1]; 
        uint8_t b = src[src_idx + 2];
        
        // 4. 颜色匹配
        int palette_index = -1;
        for (int i = 0; i < 6; i++) {
            if (spectra6_palette[i][0] == r && 
                spectra6_palette[i][1] == g && 
                spectra6_palette[i][2] == b) {
                palette_index = i;
                break;
            }
        }
        
        // 5. 索引映射
        uint8_t mapped_value = 0;
        if (palette_index >= 0) {
            mapped_value = spectra6_indices[palette_index];
        }
        
        // 6. Y4打包
        if ((dst_idx & 1) == 0) {
            // 偶数像素 - 高4位
            int packed_idx = dst_idx / 2;
            dst[packed_idx] = (mapped_value << 4) & 0xFF;
        } else {
            // 奇数像素 - 低4位
            int packed_idx = dst_idx / 2;
            uint8_t existing_value = dst[packed_idx];
            uint8_t new_lower_bits = mapped_value & 0x0F;
            dst[packed_idx] = (existing_value + new_lower_bits) & 0xFF;
        }
        
        src_idx += 3;
    }
}
```

### 简化版本
为了便于测试和理解，还提供了一个简化版本：
```c
void IndexMapping_Spectra6_AIO_Y4_Simple(uint8_t* src, uint8_t* dst, int width, int height)
```
这个版本使用硬编码的调色板，不需要外部数据。

## 数据结构

### Spectra6调色板
```c
static const uint8_t spectra6_palette[6][3] = {
    {0, 0, 0},       // 黑色
    {255, 255, 255}, // 白色  
    {255, 0, 0},     // 红色
    {0, 255, 0},     // 绿色
    {0, 0, 255},     // 蓝色
    {255, 255, 0}    // 黄色
};
```

### 索引映射表
```c
static const uint8_t spectra6_indices[6] = {0, 15, 8, 4, 2, 12};
```

## Y4格式说明

Y4格式是4位灰度格式，每个像素占用4位，两个像素打包到一个字节中：

```
字节结构: [像素0高4位][像素1低4位]
例如: 0x84 = 1000 0100
      像素0 = 8 (1000)
      像素1 = 4 (0100)
```

## 测试结果

测试输入：
- 像素0: RGB(255, 0, 0) → 红色 → 索引8
- 像素1: RGB(0, 255, 0) → 绿色 → 索引4  
- 像素2: RGB(0, 0, 255) → 蓝色 → 索引2
- 像素3: RGB(255, 255, 0) → 黄色 → 索引12

输出：
- 字节0: 0x84 (像素0=8, 像素1=4)
- 字节1: 0x2C (像素2=2, 像素3=12)

## 性能特点

1. **内存效率**: Y4格式将存储空间压缩到原来的1/6 (24位→4位)
2. **处理速度**: 直接颜色匹配，无需复杂计算
3. **颜色精度**: 限制在6种预定义颜色，适合特定显示设备

## 应用场景

- 电子纸显示器
- 单色LCD屏幕
- 低功耗显示设备
- 嵌入式系统图像处理

## 扩展可能

1. 支持更多颜色的调色板
2. 添加抖动算法提高视觉效果
3. 支持不同的打包格式
4. 优化颜色匹配算法（如使用查找表）
